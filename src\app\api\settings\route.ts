import { createServiceSupabaseClient } from '@/lib/supabase-server';
import { auth } from '@clerk/nextjs/server';
import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';

// Database row interfaces for settings tables
interface UserSettingsRow {
  id?: string;
  user_id: string;
  tenant_id: string;
  theme_preference: 'light' | 'dark' | 'system';
  preferences: Record<string, unknown>;
  created_at?: string;
  updated_at?: string;
}

interface DefaultAgentSettingsRow {
  id?: string;
  tenant_id: string;
  default_agent_id: string | null;
  is_active: boolean;
  created_by: string;
  created_at?: string;
  updated_at?: string;
}

interface AutoAssignmentRuleRow {
  id?: string;
  tenant_id: string;
  department: string;
  assigned_agent_id: string | null;
  is_active: boolean;
  priority?: number;
  created_at?: string;
  updated_at?: string;
}

// Response interfaces
interface SettingsResponse {
  user_settings:
    | UserSettingsRow
    | {
        theme_preference: string;
        preferences: Record<string, unknown>;
      };
  admin_settings?: {
    default_agent_settings: DefaultAgentSettingsRow | null;
    auto_assignment_rules: AutoAssignmentRuleRow[];
  };
}

// User Settings Schema
const UserSettingsSchema = z.object({
  theme_preference: z.enum(['light', 'dark', 'system']).optional(),
  preferences: z.record(z.unknown()).optional(),
});

// Admin Settings Schema - defined but not used in this route
// const AdminSettingsSchema = z.object({
//   default_agent_id: z.string().uuid().optional().nullable(),
//   is_active: z.boolean().optional(),
// });

// Auto Assignment Rule Schema - defined but not used in this route
// const AutoAssignmentRuleSchema = z.object({
//   department: z.enum(['sales', 'support', 'marketing', 'technical']),
//   assigned_agent_id: z.string().uuid().optional().nullable(),
//   is_default: z.boolean().optional(),
//   priority: z.number().int().min(1).max(100).optional(),
//   is_active: z.boolean().optional(),
// });

// GET - Fetch user settings and admin settings (if authorized)
export async function GET() {
  try {
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const serviceClient = createServiceSupabaseClient();

    // Get current user info
    const { data: currentUser, error: userError } = await serviceClient
      .from('users')
      .select('id, tenant_id, role')
      .eq('clerk_id', userId)
      .single();

    if (userError || !currentUser) {
      return NextResponse.json(
        { error: 'User not found in database' },
        { status: 404 }
      );
    }

    // Get user settings
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const { data: userSettings } = (await (serviceClient as any)
      .from('user_settings')
      .select('*')
      .eq('user_id', currentUser.id)
      .eq('tenant_id', currentUser.tenant_id)
      .single()) as { data: UserSettingsRow | null };

    const response: SettingsResponse = {
      user_settings: userSettings || {
        theme_preference: 'system',
        preferences: {},
      },
    };

    // If user is admin or super_admin, also fetch admin settings
    if (currentUser.role === 'admin' || currentUser.role === 'super_admin') {
      // Get default agent settings
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const { data: defaultAgentSettings } = (await (serviceClient as any)
        .from('default_agent_settings')
        .select('*')
        .eq('tenant_id', currentUser.tenant_id)
        .eq('is_active', true)
        .single()) as { data: DefaultAgentSettingsRow | null };

      // Get auto assignment rules
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const { data: autoAssignmentRules } = (await (serviceClient as any)
        .from('auto_assignment_rules')
        .select('*')
        .eq('tenant_id', currentUser.tenant_id)
        .eq('is_active', true)
        .order('priority', { ascending: true })) as {
        data: AutoAssignmentRuleRow[] | null;
      };

      response.admin_settings = {
        default_agent_settings: defaultAgentSettings,
        auto_assignment_rules: autoAssignmentRules || [],
      };
    }

    return NextResponse.json({
      success: true,
      data: response,
    });
  } catch (error) {
    console.error('Settings fetch error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch settings' },
      { status: 500 }
    );
  }
}

// PUT - Update user settings
export async function PUT(request: NextRequest) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { user_settings } = body;

    if (!user_settings) {
      return NextResponse.json(
        { error: 'user_settings is required' },
        { status: 400 }
      );
    }

    const validation = UserSettingsSchema.safeParse(user_settings);
    if (!validation.success) {
      return NextResponse.json(
        {
          error: 'Invalid user settings data',
          details: validation.error.errors,
        },
        { status: 400 }
      );
    }

    const serviceClient = createServiceSupabaseClient();

    // Get current user info
    const { data: currentUser, error: userError } = await serviceClient
      .from('users')
      .select('id, tenant_id')
      .eq('clerk_id', userId)
      .single();

    if (userError || !currentUser) {
      return NextResponse.json(
        { error: 'User not found in database' },
        { status: 404 }
      );
    }

    // Upsert user settings
    const { data: updatedSettings, error: updateError } =
      (await // eslint-disable-next-line @typescript-eslint/no-explicit-any
      (serviceClient as any)
        .from('user_settings')
        .upsert(
          {
            user_id: currentUser.id,
            tenant_id: currentUser.tenant_id,
            ...validation.data,
            updated_at: new Date().toISOString(),
          },
          {
            onConflict: 'user_id,tenant_id',
          }
        )
        .select()
        .single()) as { data: UserSettingsRow | null; error: unknown };

    if (updateError) {
      console.error('Settings update error:', updateError);
      return NextResponse.json(
        { error: 'Failed to update settings' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      data: { user_settings: updatedSettings },
    });
  } catch (error) {
    console.error('Settings update error:', error);
    return NextResponse.json(
      { error: 'Failed to update settings' },
      { status: 500 }
    );
  }
}
