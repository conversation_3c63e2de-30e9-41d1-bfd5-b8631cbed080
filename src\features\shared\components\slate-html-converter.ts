import { CustomText, SlateValue, createInitialValue } from './slate-types';
import { Text, Element as SlateElement } from 'slate';

const DEFAULT_TEXT_COLOR = 'default';

export const htmlToSlate = (html: string): SlateValue => {
  if (!html?.trim()) {
    return createInitialValue();
  }

  const cleanedHtml = html
    .replace(/&nbsp;/g, ' ')
    .replace(/&#xFEFF;/g, '')
    .replace(/<br\s*\/?>/gi, '')
    .replace(/<p[^>]*>\s*<\/p>/gi, '')
    .trim();

  if (!cleanedHtml) {
    return createInitialValue();
  }

  if (!/<[^>]*>/g.test(html)) {
    return [
      {
        type: 'paragraph',
        children: [{ text: html }],
      },
    ];
  }

  try {
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = html;

    const parseNode = (node: Node): SlateValue => {
      const result: SlateValue = [];

      for (const child of Array.from(node.childNodes)) {
        if (child.nodeType === Node.TEXT_NODE) {
          const text = child.textContent || '';
          if (text.trim()) {
            result.push({
              type: 'paragraph',
              children: [{ text }],
            });
          }
        } else if (child.nodeType === Node.ELEMENT_NODE) {
          const element = child as Element;
          const tagName = element.tagName.toLowerCase();

          switch (tagName) {
            case 'p':
            case 'div':
              result.push({
                type: 'paragraph',
                children: parseInlineElements(element),
              });
              break;
            case 'h1':
              result.push({
                type: 'heading-one',
                children: parseInlineElements(element),
              });
              break;
            case 'h2':
              result.push({
                type: 'heading-two',
                children: parseInlineElements(element),
              });
              break;
            case 'blockquote':
              result.push({
                type: 'block-quote',
                children: parseInlineElements(element),
              });
              break;
            case 'ul':
              result.push({
                type: 'bulleted-list',
                children: Array.from(element.children).map((li) => ({
                  type: 'list-item',
                  children: parseInlineElements(li),
                })),
              });
              break;
            case 'ol':
              result.push({
                type: 'numbered-list',
                children: Array.from(element.children).map((li) => ({
                  type: 'list-item',
                  children: parseInlineElements(li),
                })),
              });
              break;
            case 'br':
              result.push({
                type: 'paragraph',
                children: [{ text: '' }],
              });
              break;
            default:
              const inlineContent = parseInlineElements(element);
              if (inlineContent.length > 0) {
                result.push({
                  type: 'paragraph',
                  children: inlineContent,
                });
              }
              break;
          }
        }
      }

      return result.length > 0 ? result : createInitialValue();
    };

    const parseInlineElements = (element: Element): CustomText[] => {
      const result: CustomText[] = [];

      for (const child of Array.from(element.childNodes)) {
        if (child.nodeType === Node.TEXT_NODE) {
          const text = child.textContent || '';
          if (text) {
            result.push({ text });
          }
        } else if (child.nodeType === Node.ELEMENT_NODE) {
          const el = child as Element;
          const tagName = el.tagName.toLowerCase();
          const text = el.textContent || '';

          if (text) {
            const textNode: CustomText = { text };

            if (tagName === 'strong' || tagName === 'b') {
              textNode.bold = true;
            }
            if (tagName === 'em' || tagName === 'i') {
              textNode.italic = true;
            }
            if (tagName === 'u') {
              textNode.underline = true;
            }

            const style = el.getAttribute('style');
            if (style) {
              const colorMatch = style.match(/color:\s*([^;]+)/);
              if (colorMatch && colorMatch[1]) {
                textNode.color = colorMatch[1].trim();
              }
            }

            result.push(textNode);
          }
        }
      }

      return result.length > 0 ? result : [{ text: '' }];
    };

    return parseNode(tempDiv);
  } catch (error) {
    console.warn('Failed to parse HTML, falling back to plain text:', error);
    return [
      {
        type: 'paragraph',
        children: [{ text: html.replace(/<[^>]*>/g, '') }],
      },
    ];
  }
};

const processTextNode = (child: CustomText): string => {
  let text = child.text;
  if (child.color && child.color !== DEFAULT_TEXT_COLOR)
    text = `<span style="color: ${child.color}">${text}</span>`;
  if (child.bold) text = `<strong>${text}</strong>`;
  if (child.italic) text = `<em>${text}</em>`;
  if (child.underline)
    text = `<u style="text-decoration-color: currentColor">${text}</u>`;
  return text;
};

const processSlateNode = (
  node: SlateElement,
  isInitialParagraph = false
): string => {
  const children = node.children
    .map((child) => {
      if (Text.isText(child)) {
        return processTextNode(child);
      } else if (SlateElement.isElement(child)) {
        return processSlateNode(child, false);
      }
      return '';
    })
    .join('');

  if (node.type === 'block-quote') {
    const hasCustomColor = node.children.some(
      (child) =>
        Text.isText(child) && child.color && child.color !== DEFAULT_TEXT_COLOR
    );
    const customColor = hasCustomColor
      ? node.children.find(
          (child) =>
            Text.isText(child) &&
            child.color &&
            child.color !== DEFAULT_TEXT_COLOR
        )?.color
      : null;

    const borderStyle = customColor
      ? `border-left: 4px solid ${customColor};`
      : 'border-left: 4px solid hsl(var(--border));';
    const textStyle = hasCustomColor
      ? ''
      : 'color: hsl(var(--muted-foreground));';

    return `<blockquote style="${borderStyle} padding-left: 1rem; font-style: italic; margin: 0.5rem 0; ${textStyle}">${children}</blockquote>`;
  }

  const tagMap = {
    'heading-one': 'h1',
    'heading-two': 'h2',
    'bulleted-list': 'ul',
    'numbered-list': 'ol',
    'list-item': 'li',
  };
  const tag = tagMap[node.type as keyof typeof tagMap] || 'p';

  const isEmpty = children.trim() === '';
  const content = isEmpty ? '&nbsp;' : children;

  if (tag === 'p' && isEmpty && !isInitialParagraph) {
    return `<p class="empty-paragraph">${content}</p>`;
  }

  return `<${tag}>${content}</${tag}>`;
};

const isSlateValueInitial = (value: SlateValue): boolean => {
  return (
    value.length === 1 &&
    SlateElement.isElement(value[0]) &&
    value[0].type === 'paragraph' &&
    value[0].children.length === 1 &&
    Text.isText(value[0].children[0]) &&
    value[0].children[0].text === ''
  );
};

export const slateToHtml = (value: SlateValue): string => {
  const isInitial = isSlateValueInitial(value);

  return value
    .map((node, index) => {
      if (!SlateElement.isElement(node)) return '';
      return processSlateNode(node, isInitial && index === 0);
    })
    .join('');
};
