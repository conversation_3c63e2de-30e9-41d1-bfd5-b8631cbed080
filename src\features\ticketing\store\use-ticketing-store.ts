/**
 * Ticketing Store - Optimized for Real-Time Performance with Zustand v5
 *
 * This Zustand store provides smart incremental updates that only add NEW content
 * without touching existing data, ensuring zero impact on existing UI state.
 *
 * Key Features:
 * - Smart incremental updates (addTicketIncremental, updateTicketIncremental)
 * - Silent cache loading without loading states
 * - Background API synchronization
 * - Optimistic updates with rollback support
 * - Zustand v5 useShallow patterns for selective rendering
 * - Efficient state management following YAGNI/KISS principles
 *
 * <AUTHOR> Augster
 * @version 3.0 - Zustand v5 Optimized (January 2025)
 */

import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';
import { useShallow } from 'zustand/react/shallow';
import { Ticket } from '@/features/ticketing/models/ticket.schema';
import { cacheService } from '@/lib/cache/cache-service';
import { RoleBasedFilterContext } from '@/features/ticketing/types/role-based-filtering';
import { filterTicketsByRole } from '@/features/ticketing/utils/role-based-filtering';

interface TicketingState {
  tickets: Ticket[];
  selectedTicketId: string | null;
  currentTenantId: string | null;
  // useMockData removed for production
  isLoading: boolean;
  isCacheLoaded: boolean;
  lastCacheSync: number;
  optimisticUpdates: Record<string, Partial<Ticket>>;
}

interface TicketingActions {
  setTenantId: (tenantId: string | null) => void;
  selectTicket: (ticketId: string | null) => void;
  updateTicket: (ticketId: string, updates: Partial<Ticket>) => void;
  updateTicketOptimistic: (ticketId: string, updates: Partial<Ticket>) => void;
  addTicket: (ticket: Omit<Ticket, 'id' | 'createdAt' | 'updatedAt'>) => string;
  addTicketOptimistic: (ticket: Ticket) => void;
  addTicketIncremental: (ticket: Ticket) => void;
  updateTicketIncremental: (ticketId: string, updates: Partial<Ticket>) => void;
  getTicketsForTenant: (tenantId: string | null) => Ticket[];
  // Mock data functions removed for production
  loadTicketsFromAPI: (
    tenantId: string,
    userRole?: string,
    roleFilter?: string
  ) => Promise<void>;
  loadTicketsFromCacheSilent: (tenantId: string) => Promise<void>;
  syncWithCache: (tenantId: string) => Promise<void>;
  clearOptimisticUpdate: (ticketId: string) => void;
  setTickets: (tickets: Ticket[]) => void;
  clearCacheForTenant: (tenantId: string) => Promise<void>;
  resetStore: () => void;
}

export const useTicketingStore = create<TicketingState & TicketingActions>()(
  devtools(
    persist(
      (set, get) => ({
        // State
        tickets: [], // Start with empty tickets array, will be populated from cache/Supabase
        selectedTicketId: null,
        currentTenantId: null,
        // useMockData removed for production
        isLoading: false,
        isCacheLoaded: false,
        lastCacheSync: 0,
        optimisticUpdates: {},

        // Actions
        setTenantId: (tenantId) => {
          set({ currentTenantId: tenantId });
          // Load cache silently when tenant changes
          if (tenantId) {
            get().loadTicketsFromCacheSilent(tenantId);
          }
        },

        selectTicket: (ticketId) => set({ selectedTicketId: ticketId }),

        updateTicket: (ticketId, updates) =>
          set((state) => ({
            tickets: state.tickets.map((ticket) =>
              ticket.id === ticketId
                ? { ...ticket, ...updates, updatedAt: new Date() }
                : ticket
            ),
          })),

        updateTicketOptimistic: (ticketId, updates) => {
          // Apply optimistic update immediately
          set((state) => ({
            tickets: state.tickets.map((ticket) =>
              ticket.id === ticketId
                ? { ...ticket, ...updates, updatedAt: new Date() }
                : ticket
            ),
            optimisticUpdates: {
              ...state.optimisticUpdates,
              [ticketId]: { ...state.optimisticUpdates[ticketId], ...updates },
            },
          }));

          // Update cache
          const { currentTenantId } = get();
          if (currentTenantId) {
            cacheService.updateTicketInCache(
              currentTenantId,
              ticketId,
              updates
            );
          }
        },

        addTicket: (ticketData) => {
          const newTicketId = `ticket_${Date.now()}_${Math.random()
            .toString(36)
            .substring(2, 11)}`;
          const now = new Date();

          const newTicket: Ticket = {
            ...ticketData,
            id: newTicketId,
            createdAt: now,
            updatedAt: now,
          };

          set((state) => ({
            tickets: [newTicket, ...state.tickets],
            selectedTicketId: newTicketId,
          }));

          return newTicketId;
        },

        addTicketOptimistic: (ticket) => {
          // Add ticket optimistically
          set((state) => ({
            tickets: [ticket, ...state.tickets],
          }));

          // Add to cache
          const { currentTenantId } = get();
          if (currentTenantId) {
            cacheService.addTicketToCache(currentTenantId, ticket);
          }
        },

        /**
         * Smart incremental add - only adds NEW tickets without touching existing data
         * Used by real-time updates to ensure zero impact on existing UI state
         */
        addTicketIncremental: (ticket) => {
          set((state) => {
            // Check if ticket already exists to avoid duplicates
            const existingTicket = state.tickets.find(
              (t) => t.id === ticket.id
            );
            if (existingTicket) {
              console.log(
                '🔄 Ticket already exists, skipping incremental add:',
                ticket.id
              );
              return state; // No change to existing data
            }

            console.log('✨ Adding new ticket incrementally:', ticket.title);
            return {
              tickets: [ticket, ...state.tickets],
            };
          });

          // Add to cache silently
          const { currentTenantId } = get();
          if (currentTenantId) {
            cacheService.addTicketToCache(currentTenantId, ticket);
          }
        },

        /**
         * Smart incremental update - only updates specific fields without full re-render
         * Preserves existing UI state and only modifies changed data
         */
        updateTicketIncremental: (ticketId, updates) => {
          set((state) => {
            const existingTicket = state.tickets.find((t) => t.id === ticketId);
            if (!existingTicket) {
              console.log(
                '🚫 Ticket not found for incremental update:',
                ticketId
              );
              return state; // No change if ticket doesn't exist
            }

            // Only update if there are actual changes
            const hasChanges = Object.keys(updates).some(
              (key) =>
                existingTicket[key as keyof Ticket] !==
                updates[key as keyof Ticket]
            );

            if (!hasChanges) {
              console.log(
                '🔄 No changes detected, skipping incremental update:',
                ticketId
              );
              return state; // No change if no actual updates
            }

            console.log('🔧 Updating ticket incrementally:', ticketId, updates);
            return {
              tickets: state.tickets.map((ticket) =>
                ticket.id === ticketId
                  ? { ...ticket, ...updates, updatedAt: new Date() }
                  : ticket
              ),
            };
          });

          // Update cache silently
          const { currentTenantId } = get();
          if (currentTenantId) {
            cacheService.updateTicketInCache(
              currentTenantId,
              ticketId,
              updates
            );
          }
        },

        loadTicketsFromCache: async (tenantId: string) => {
          try {
            set({ isLoading: true });
            const cachedTickets = await cacheService.getCachedTickets(tenantId);

            if (cachedTickets.length > 0) {
              set({
                tickets: cachedTickets,
                isCacheLoaded: true,
                isLoading: false,
              });
            } else {
              set({ isLoading: false });
            }
          } catch (error) {
            console.error('Failed to load tickets from cache:', error);
            set({ isLoading: false });
          }
        },

        /**
         * Silent cache loading - loads tickets from cache without any loading states
         * Provides instant UI rendering for seamless user experience
         */
        loadTicketsFromCacheSilent: async (tenantId) => {
          try {
            console.log(
              '🔄 Loading tickets from cache silently for tenant:',
              tenantId
            );
            const cachedTickets = await cacheService.getCachedTickets(tenantId);

            if (cachedTickets.length > 0) {
              set({
                tickets: cachedTickets,
                isCacheLoaded: true,
              });
              console.log(
                '✅ Loaded',
                cachedTickets.length,
                'tickets from cache silently'
              );
            } else {
              console.log('📭 No cached tickets found for tenant:', tenantId);
              set({ isCacheLoaded: true }); // Mark as loaded even if empty
            }
          } catch (error) {
            console.error('Failed to load tickets from cache silently:', error);
            set({ isCacheLoaded: true }); // Mark as loaded to prevent retry loops
          }
        },

        syncWithCache: async (tenantId) => {
          try {
            // Perform delta sync
            await cacheService.performDeltaSync(tenantId);

            // Reload from cache
            const cachedTickets = await cacheService.getCachedTickets(tenantId);
            set({
              tickets: cachedTickets,
              lastCacheSync: Date.now(),
            });
          } catch (error) {
            console.error('Cache sync failed:', error);
          }
        },

        clearOptimisticUpdate: (ticketId) => {
          set((state) => {
            // eslint-disable-next-line @typescript-eslint/no-unused-vars
            const { [ticketId]: _removed, ...remaining } =
              state.optimisticUpdates;
            return { optimisticUpdates: remaining };
          });
        },

        clearCacheForTenant: async (tenantId) => {
          await cacheService.clearCache(tenantId);
          set({
            tickets: [],
            isCacheLoaded: false,
            lastCacheSync: 0,
            optimisticUpdates: {},
          });
        },

        getTicketsForTenant: (tenantId) => {
          const state = get();

          // Apply optimistic updates to tickets
          const ticketsWithOptimistic = state.tickets.map((ticket) => {
            const optimisticUpdate = state.optimisticUpdates[ticket.id];
            return optimisticUpdate
              ? { ...ticket, ...optimisticUpdate }
              : ticket;
          });

          // Return real tickets from cache/Supabase
          if (!tenantId) {
            // For localhost, return all tickets
            return ticketsWithOptimistic;
          }
          // Filter tickets by tenant ID
          return ticketsWithOptimistic.filter(
            (ticket) => ticket.tenantId === tenantId
          );
        },

        // Mock data functions removed for production

        setTickets: (tickets) => {
          set({ tickets });
          // Cache the tickets
          const { currentTenantId } = get();
          if (currentTenantId) {
            cacheService.cacheTickets(currentTenantId, tickets);
          }
        },

        loadTicketsFromAPI: async (
          tenantId,
          userRole = 'user', // Keep for backward compatibility but not used in API
          roleFilter = 'all'
        ) => {
          try {
            // Build URL with role-based parameters for proper filtering
            // Note: userRole is now determined from Clerk JWT claims in the API
            const params = new URLSearchParams({
              tenant_id: tenantId,
              role_filter: roleFilter,
            });

            const response = await fetch(`/api/tickets?${params.toString()}`, {
              signal: AbortSignal.timeout(8000),
              headers: { 'Content-Type': 'application/json' },
            });

            if (!response.ok) {
              const errorData = await response.json().catch(() => ({}));
              throw new Error(
                `Failed to fetch tickets: ${
                  errorData.error || response.statusText
                }`
              );
            }

            const tickets = await response.json();

            // Only update if we have new data and avoid unnecessary re-renders
            const currentState = get();
            if (
              tickets.length !== currentState.tickets.length ||
              tickets.some(
                (ticket: Ticket, index: number) =>
                  !currentState.tickets[index] ||
                  ticket.id !== currentState.tickets[index].id
              )
            ) {
              console.log(
                '📊 Updating tickets from API:',
                tickets.length,
                'tickets for role:',
                userRole
              );
              set({ tickets });
            } else {
              console.log('🔄 API data matches cache, no update needed');
            }

            // Cache the fetched tickets silently
            await cacheService.cacheTickets(tenantId, tickets);
          } catch (error) {
            console.error('Background API load failed:', error);
            // Don't throw error to avoid disrupting UI
          }
        },

        resetStore: () => {
          console.log('🔄 Resetting ticketing store to initial state');
          set({
            tickets: [],
            selectedTicketId: null,
            currentTenantId: null,
            isLoading: false,
            isCacheLoaded: false,
            lastCacheSync: 0,
            optimisticUpdates: {},
          });
        },
      }),
      {
        name: 'ticketing-store',
        partialize: (state) => ({
          currentTenantId: state.currentTenantId,
          lastCacheSync: state.lastCacheSync,
          // Don't persist tickets, responses, or optimistic updates - they come from cache
        }),
      }
    ),
    {
      name: 'ticketing-store-devtools',
    }
  )
);

// Optimized selectors using useShallow for selective rendering (Zustand v5)
export const useTicketingSelectors = {
  // Basic state selectors
  useTickets: () => useTicketingStore((state) => state.tickets),
  useSelectedTicketId: () =>
    useTicketingStore((state) => state.selectedTicketId),
  useCurrentTenantId: () => useTicketingStore((state) => state.currentTenantId),
  useIsLoading: () => useTicketingStore((state) => state.isLoading),
  useIsCacheLoaded: () => useTicketingStore((state) => state.isCacheLoaded),

  // Multi-property selectors with useShallow for performance
  useTicketingState: () =>
    useTicketingStore(
      useShallow((state) => ({
        tickets: state.tickets,
        selectedTicketId: state.selectedTicketId,
        currentTenantId: state.currentTenantId,
        isLoading: state.isLoading,
        isCacheLoaded: state.isCacheLoaded,
      }))
    ),

  useTicketingActions: () =>
    useTicketingStore(
      useShallow((state) => ({
        setTenantId: state.setTenantId,
        selectTicket: state.selectTicket,
        updateTicket: state.updateTicket,
        addTicket: state.addTicket,
        addTicketOptimistic: state.addTicketOptimistic,
        addTicketIncremental: state.addTicketIncremental,
        updateTicketIncremental: state.updateTicketIncremental,
        loadTicketsFromAPI: state.loadTicketsFromAPI,
        loadTicketsFromCacheSilent: state.loadTicketsFromCacheSilent,
        setTickets: state.setTickets,
        resetStore: state.resetStore,
      }))
    ),

  useOptimisticState: () =>
    useTicketingStore(
      useShallow((state) => ({
        optimisticUpdates: state.optimisticUpdates,
        updateTicketOptimistic: state.updateTicketOptimistic,
        clearOptimisticUpdate: state.clearOptimisticUpdate,
      }))
    ),

  useCacheState: () =>
    useTicketingStore(
      useShallow((state) => ({
        isCacheLoaded: state.isCacheLoaded,
        lastCacheSync: state.lastCacheSync,
        syncWithCache: state.syncWithCache,
        clearCacheForTenant: state.clearCacheForTenant,
      }))
    ),

  // Computed selectors with memoization
  useTicketsForTenant: (tenantId: string | null) =>
    useTicketingStore(
      useShallow((state) => {
        const ticketsWithOptimistic = state.tickets.map((ticket) => {
          const optimisticUpdate = state.optimisticUpdates[ticket.id];
          return optimisticUpdate ? { ...ticket, ...optimisticUpdate } : ticket;
        });

        if (!tenantId) {
          return ticketsWithOptimistic;
        }
        return ticketsWithOptimistic.filter(
          (ticket) => ticket.tenantId === tenantId
        );
      })
    ),

  useSelectedTicket: () =>
    useTicketingStore(
      useShallow((state) => {
        if (!state.selectedTicketId) return null;
        const ticket = state.tickets.find(
          (t) => t.id === state.selectedTicketId
        );
        if (!ticket) return null;

        const optimisticUpdate = state.optimisticUpdates[ticket.id];
        return optimisticUpdate ? { ...ticket, ...optimisticUpdate } : ticket;
      })
    ),

  // Role-based filtering selectors
  useRoleBasedTickets: (context: RoleBasedFilterContext) =>
    useTicketingStore(
      useShallow((state) => {
        const ticketsWithOptimistic = state.tickets.map((ticket) => {
          const optimisticUpdate = state.optimisticUpdates[ticket.id];
          return optimisticUpdate ? { ...ticket, ...optimisticUpdate } : ticket;
        });

        // Filter by tenant first
        const tenantTickets = context.tenantId
          ? ticketsWithOptimistic.filter(
              (ticket) => ticket.tenantId === context.tenantId
            )
          : ticketsWithOptimistic;

        // Apply role-based filtering
        return filterTicketsByRole(tenantTickets, context);
      })
    ),

  useAssignedTickets: (context: RoleBasedFilterContext) =>
    useTicketingStore(
      useShallow((state) => {
        const ticketsWithOptimistic = state.tickets.map((ticket) => {
          const optimisticUpdate = state.optimisticUpdates[ticket.id];
          return optimisticUpdate ? { ...ticket, ...optimisticUpdate } : ticket;
        });

        // Filter by tenant first
        const tenantTickets = context.tenantId
          ? ticketsWithOptimistic.filter(
              (ticket) => ticket.tenantId === context.tenantId
            )
          : ticketsWithOptimistic;

        // Apply role-based filtering for assigned tickets only
        // CRITICAL: For agents, exclude 'new' status from assigned tickets (they should only see opened tickets)
        const assignedTicketStatuses =
          context.role === 'agent'
            ? ['open', 'pending'] // Agents: exclude 'new' status
            : ['new', 'open', 'pending']; // Admin/Super Admin: can see all statuses

        const result = filterTicketsByRole(tenantTickets, context, {
          assignedOnly: true,
          status: assignedTicketStatuses,
        });

        return result.tickets;
      })
    ),

  useMyOpenTickets: (context: RoleBasedFilterContext) =>
    useTicketingStore(
      useShallow((state) => {
        const ticketsWithOptimistic = state.tickets.map((ticket) => {
          const optimisticUpdate = state.optimisticUpdates[ticket.id];
          return optimisticUpdate ? { ...ticket, ...optimisticUpdate } : ticket;
        });

        // Filter by tenant first
        const tenantTickets = context.tenantId
          ? ticketsWithOptimistic.filter(
              (ticket) => ticket.tenantId === context.tenantId
            )
          : ticketsWithOptimistic;

        // Apply role-based filtering for open tickets
        const result = filterTicketsByRole(tenantTickets, context, {
          status: ['open'],
        });

        return result.tickets;
      })
    ),
};
