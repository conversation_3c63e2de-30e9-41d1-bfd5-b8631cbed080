import React from 'react';
import { But<PERSON> } from '@/features/shared/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/features/shared/components/ui/select';
import { Separator } from '@/features/shared/components/ui/separator';
import {
  Bold,
  Italic,
  Underline,
  List,
  ListOrdered,
  Palette,
  Quote,
  AlignLeft,
  AlignCenter,
  AlignRight,
  Paperclip,
  type LucideIcon,
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { HexColorPicker } from 'react-colorful';
import { CustomEditor, CustomText } from './slate-types';

interface ToolbarProps {
  editor: CustomEditor;
  disabled: boolean;
  onAttachClick: (() => void) | undefined;
  toggleMark: (format: keyof Omit<CustomText, 'text'>) => void;
  toggleBlock: (format: string) => void;
  currentTextColor: string;
  handleTextColorChange: (color: string) => void;
  resetTextColor: () => void;
  showTextColorPicker: boolean;
  setShowTextColorPicker: (show: boolean) => void;
  colorPickerRef: React.RefObject<HTMLDivElement>;
}

const DEFAULT_TEXT_COLOR = 'default';

const colorPickerStyles = `
  .react-colorful { width: 200px; height: 200px; }
  .react-colorful__saturation { width: 200px; height: 150px; border-radius: 8px 8px 0 0; }
  .react-colorful__hue { height: 24px; border-radius: 0 0 8px 8px; }
  .react-colorful__pointer { width: 16px; height: 16px; border: 2px solid #fff; border-radius: 50%; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2); }
`;

const ToolbarButton = ({
  icon: Icon,
  isActive,
  onClick,
  disabled,
}: {
  format: string;
  icon: LucideIcon;
  isBlock?: boolean;
  isActive: boolean;
  onClick: () => void;
  disabled: boolean;
}) => (
  <Button
    type='button'
    variant='ghost'
    size='sm'
    className={cn(
      'h-8 w-8 p-0 hover:bg-gray-100 dark:hover:bg-gray-700',
      isActive && 'bg-gray-200 dark:bg-gray-600'
    )}
    onClick={onClick}
    disabled={disabled}
  >
    <Icon className='h-4 w-4' />
  </Button>
);

export function Toolbar({
  disabled,
  onAttachClick,
  toggleMark,
  toggleBlock,
  currentTextColor,
  handleTextColorChange,
  resetTextColor,
  showTextColorPicker,
  setShowTextColorPicker,
  colorPickerRef,
}: ToolbarProps) {
  return (
    <div className='space-y-2 mb-4'>
      <div className='flex items-center gap-1'>
        <Select
          key='format-select'
          defaultValue='paragraph'
          onValueChange={(value) => {
            if (disabled) return;
            if (value === 'heading1') {
              toggleBlock('heading-one');
            } else if (value === 'heading2') {
              toggleBlock('heading-two');
            } else if (value === 'blockquote') {
              toggleBlock('block-quote');
            } else {
              toggleBlock('paragraph');
            }
          }}
        >
          <SelectTrigger className='w-32 h-8'>
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value='paragraph'>
              <span className='text-sm'>Paragraph</span>
            </SelectItem>
            <SelectItem value='heading1'>
              <span className='text-lg font-bold'>Heading 1</span>
            </SelectItem>
            <SelectItem value='heading2'>
              <span className='text-base font-semibold'>Heading 2</span>
            </SelectItem>
            <SelectItem value='blockquote'>
              <span className='text-sm italic text-gray-600'>Quote</span>
            </SelectItem>
          </SelectContent>
        </Select>

        <Separator orientation='vertical' className='h-6 mx-1' />

        <ToolbarButton
          format='bold'
          icon={Bold}
          isActive={false}
          onClick={() => toggleMark('bold')}
          disabled={disabled}
        />
        <ToolbarButton
          format='italic'
          icon={Italic}
          isActive={false}
          onClick={() => toggleMark('italic')}
          disabled={disabled}
        />
        <ToolbarButton
          format='underline'
          icon={Underline}
          isActive={false}
          onClick={() => toggleMark('underline')}
          disabled={disabled}
        />

        <div className='relative color-picker-container'>
          <Button
            type='button'
            variant='ghost'
            size='sm'
            className={cn(
              'h-8 w-8 p-0 relative transition-all duration-200',
              showTextColorPicker
                ? 'bg-gray-200 dark:bg-gray-600'
                : 'hover:bg-gray-100 dark:hover:bg-gray-700'
            )}
            onClick={() => setShowTextColorPicker(!showTextColorPicker)}
            disabled={disabled}
            title='Text Color'
          >
            <Palette className='h-4 w-4' />
            {currentTextColor !== DEFAULT_TEXT_COLOR && (
              <div
                className='absolute bottom-0 left-0 right-0 h-1 rounded-b'
                style={{ backgroundColor: currentTextColor }}
              />
            )}
          </Button>

          {showTextColorPicker && (
            <div
              ref={colorPickerRef}
              className='absolute top-10 left-0 z-50 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg p-3'
            >
              <div className='text-xs font-medium text-gray-700 dark:text-gray-300 mb-2'>
                Text Color
              </div>
              <style>{colorPickerStyles}</style>
              <HexColorPicker
                color={currentTextColor}
                onChange={handleTextColorChange}
              />
              <div className='mt-2 flex items-center gap-2'>
                <button
                  type='button'
                  className='px-2 py-1 text-xs bg-gray-100 dark:bg-gray-700 rounded hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors'
                  onClick={resetTextColor}
                >
                  Reset
                </button>
                <div className='text-xs text-gray-500 dark:text-gray-400'>
                  {currentTextColor === DEFAULT_TEXT_COLOR
                    ? 'Default'
                    : currentTextColor.toUpperCase()}
                </div>
              </div>
            </div>
          )}
        </div>

        <Separator orientation='vertical' className='h-6 mx-1' />

        <ToolbarButton
          format='block-quote'
          icon={Quote}
          isBlock
          isActive={false}
          onClick={() => toggleBlock('block-quote')}
          disabled={disabled}
        />

        <Separator orientation='vertical' className='h-6 mx-1' />

        <ToolbarButton
          format='align-left'
          icon={AlignLeft}
          isBlock
          isActive={false}
          onClick={() => toggleBlock('align-left')}
          disabled={disabled}
        />
        <ToolbarButton
          format='align-center'
          icon={AlignCenter}
          isBlock
          isActive={false}
          onClick={() => toggleBlock('align-center')}
          disabled={disabled}
        />
        <ToolbarButton
          format='align-right'
          icon={AlignRight}
          isBlock
          isActive={false}
          onClick={() => toggleBlock('align-right')}
          disabled={disabled}
        />

        <Separator orientation='vertical' className='h-6 mx-1' />

        <ToolbarButton
          format='bulleted-list'
          icon={List}
          isBlock
          isActive={false}
          onClick={() => toggleBlock('bulleted-list')}
          disabled={disabled}
        />
        <ToolbarButton
          format='numbered-list'
          icon={ListOrdered}
          isBlock
          isActive={false}
          onClick={() => toggleBlock('numbered-list')}
          disabled={disabled}
        />

        <Separator orientation='vertical' className='h-6 mx-1' />

        <Button
          type='button'
          variant='ghost'
          size='sm'
          className='h-8 w-8 p-0 hover:bg-gray-100 dark:hover:bg-gray-700'
          onClick={() => {
            onAttachClick?.();
          }}
          disabled={disabled}
          title='Attach File'
        >
          <Paperclip className='h-4 w-4' />
        </Button>
      </div>
    </div>
  );
}
