'use client';

import { Paperclip } from 'lucide-react';

function AttachmentIndicator({ count }: { count: number }) {
  if (count === 0) return null;

  return (
    <div className='flex items-center gap-1 px-2 py-1 bg-blue-50 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400 rounded-md text-xs font-medium'>
      <Paperclip className='h-3 w-3' />
      <span>{count}</span>
    </div>
  );
}

export default AttachmentIndicator;
