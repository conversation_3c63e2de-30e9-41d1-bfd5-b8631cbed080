import { cacheDB, CacheMetadata } from '@/lib/cache/dexie-db';
import {
  AdminSettings,
  DEFAULT_USER_SETTINGS,
  SettingsResponse,
  UserSettings,
} from '../models/settings.schema';

interface CachedSettings {
  id: string;
  tenant_id: string;
  user_settings: UserSettings | null;
  admin_settings: AdminSettings | null;
  cached_at: number;
  version: number;
}

/**
 * Settings cache service for instant loading and offline support
 */
export class SettingsCacheService {
  private static instance: SettingsCacheService;
  private readonly CACHE_VERSION = 1;
  private readonly CACHE_EXPIRY = 5 * 60 * 1000; // 5 minutes

  private constructor() {}

  static getInstance(): SettingsCacheService {
    if (!SettingsCacheService.instance) {
      SettingsCacheService.instance = new SettingsCacheService();
    }
    return SettingsCacheService.instance;
  }

  /**
   * Gets cache key for settings
   */
  private getCacheKey(tenantId: string, userId: string): string {
    return `settings_${tenantId}_${userId}`;
  }

  /**
   * Gets settings from cache
   */
  async getFromCache(
    tenantId: string,
    userId: string
  ): Promise<SettingsResponse | null> {
    try {
      const cacheKey = this.getCacheKey(tenantId, userId);

      // Get cached settings
      const cached = (await cacheDB.metadata
        .where('id')
        .equals(cacheKey)
        .first()) as CacheMetadata & { settings?: CachedSettings };

      if (!cached || !cached.settings) {
        return null;
      }

      // Check if cache is expired
      const now = Date.now();
      if (now - cached.settings.cached_at > this.CACHE_EXPIRY) {
        await this.clearCache(tenantId, userId);
        return null;
      }

      // Check version compatibility
      if (cached.settings.version !== this.CACHE_VERSION) {
        await this.clearCache(tenantId, userId);
        return null;
      }

      return {
        user_settings:
          cached.settings.user_settings ||
          ({
            ...DEFAULT_USER_SETTINGS,
            user_id: userId,
            tenant_id: tenantId,
          } as UserSettings),
        admin_settings: cached.settings.admin_settings || undefined,
      };
    } catch (error) {
      return null;
    }
  }

  /**
   * Stores settings in cache
   */
  async storeInCache(
    tenantId: string,
    userId: string,
    settings: SettingsResponse
  ): Promise<void> {
    try {
      const cacheKey = this.getCacheKey(tenantId, userId);
      const now = Date.now();

      const cachedSettings: CachedSettings = {
        id: cacheKey,
        tenant_id: tenantId,
        user_settings: settings.user_settings,
        admin_settings: settings.admin_settings || null,
        cached_at: now,
        version: this.CACHE_VERSION,
      };

      // Store in metadata table
      await cacheDB.metadata.put({
        id: cacheKey,
        tenant_id: tenantId,
        table_name: 'settings',
        last_sync: now,
        version: this.CACHE_VERSION,
        cached_at: now,
        settings: cachedSettings,
      } as CacheMetadata & { settings: CachedSettings });
    } catch (error) {
      // Silent fail for cache operations
    }
  }

  /**
   * Updates specific user settings in cache
   */
  async updateUserSettingsInCache(
    tenantId: string,
    userId: string,
    userSettings: UserSettings
  ): Promise<void> {
    try {
      const cached = await this.getFromCache(tenantId, userId);
      if (cached) {
        await this.storeInCache(tenantId, userId, {
          user_settings: userSettings,
          admin_settings: cached.admin_settings,
        });
      }
    } catch (error) {
      // Silent fail for cache operations
    }
  }

  /**
   * Updates admin settings in cache
   */
  async updateAdminSettingsInCache(
    tenantId: string,
    userId: string,
    adminSettings: AdminSettings
  ): Promise<void> {
    try {
      const cached = await this.getFromCache(tenantId, userId);
      if (cached) {
        await this.storeInCache(tenantId, userId, {
          user_settings: cached.user_settings,
          admin_settings: adminSettings,
        });
      }
    } catch (error) {
      // Silent fail for cache operations
    }
  }

  /**
   * Clears settings cache for a specific user
   */
  async clearCache(tenantId: string, userId: string): Promise<void> {
    try {
      const cacheKey = this.getCacheKey(tenantId, userId);
      await cacheDB.metadata.delete(cacheKey);
    } catch (error) {
      // Silent fail for cache operations
    }
  }

  /**
   * Clears all settings cache for a tenant
   */
  async clearTenantCache(tenantId: string): Promise<void> {
    try {
      await cacheDB.metadata
        .where('tenant_id')
        .equals(tenantId)
        .and((item) => item.table_name === 'settings')
        .delete();
    } catch (error) {
      // Silent fail for cache operations
    }
  }

  /**
   * Gets cache statistics
   */
  async getCacheStats(tenantId: string): Promise<{
    totalEntries: number;
    oldestEntry: number | null;
    newestEntry: number | null;
    totalSize: number;
  }> {
    try {
      const entries = await cacheDB.metadata
        .where('tenant_id')
        .equals(tenantId)
        .and((item) => item.table_name === 'settings')
        .toArray();

      if (entries.length === 0) {
        return {
          totalEntries: 0,
          oldestEntry: null,
          newestEntry: null,
          totalSize: 0,
        };
      }

      const timestamps = entries
        .map((entry) => entry.cached_at)
        .filter(Boolean) as number[];

      return {
        totalEntries: entries.length,
        oldestEntry: timestamps.length > 0 ? Math.min(...timestamps) : null,
        newestEntry: timestamps.length > 0 ? Math.max(...timestamps) : null,
        totalSize: JSON.stringify(entries).length,
      };
    } catch (error) {
      return {
        totalEntries: 0,
        oldestEntry: null,
        newestEntry: null,
        totalSize: 0,
      };
    }
  }

  /**
   * Checks if cache is available and working
   */
  async isCacheAvailable(): Promise<boolean> {
    try {
      // Try to perform a simple operation
      await cacheDB.metadata.limit(1).toArray();
      return true;
    } catch (error) {
      return false;
    }
  }

  /**
   * Preloads settings for faster access
   */
  async preloadSettings(tenantId: string, userId: string): Promise<void> {
    try {
      // Check if already cached
      const cached = await this.getFromCache(tenantId, userId);
      if (cached) {
        return; // Already cached
      }

      // Fetch from API and cache
      const response = await fetch('/api/settings');
      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          await this.storeInCache(tenantId, userId, data.data);
        }
      }
    } catch (error) {
      // Silent fail for cache operations
    }
  }
}
