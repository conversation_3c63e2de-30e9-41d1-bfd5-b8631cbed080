'use client';

import { useState, useCallback, useEffect, useMemo, useRef } from 'react';

interface User {
  id: string;
  email: string;
  name: string;
  role: string;
  status: string;
  avatar_url?: string | null;
  clerk_id?: string;
}

interface UseUserSearchOptions {
  roleFilter?: string;
  limit?: number;
}

interface UseUserSearchReturn {
  users: User[];
  isLoading: boolean;
  error: string | null;
  searchUsers: (query: string) => Promise<void>;
  clearUsers: () => void;
}

export function useUserSearch(
  options: UseUserSearchOptions = {}
): UseUserSearchReturn {
  const { roleFilter, limit = 10 } = options;
  const [users, setUsers] = useState<User[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const searchUsers = useCallback(
    async (query: string) => {
      if (query.length < 2) {
        setUsers([]);
        setError(null);
        return;
      }

      setIsLoading(true);
      setError(null);

      try {
        const params = new URLSearchParams({
          q: query,
          limit: limit.toString(),
        });

        if (roleFilter) {
          params.append('role', roleFilter);
        }

        const response = await fetch(`/api/users/search?${params}`);

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to search users');
        }

        const data = await response.json();
        setUsers(data.users || []);
      } catch (err) {
        const errorMessage =
          err instanceof Error ? err.message : 'An error occurred';
        setError(errorMessage);
        setUsers([]);
      } finally {
        setIsLoading(false);
      }
    },
    [roleFilter, limit]
  );

  const clearUsers = useCallback(() => {
    setUsers([]);
    setError(null);
  }, []);

  return {
    users,
    isLoading,
    error,
    searchUsers,
    clearUsers,
  };
}

// Global cache for user details to prevent redundant API calls
const userDetailsCache = new Map<string, User>();
const pendingRequests = new Map<string, Promise<User | null>>();

/**
 * Hook for getting user details by IDs with caching and performance optimization
 */
export function useUserDetails(userIds: string[]) {
  const [users, setUsers] = useState<User[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Memoize the sorted userIds to prevent unnecessary re-renders
  const memoizedUserIds = useMemo(() => {
    return [...userIds].sort();
  }, [userIds]);

  // Create a stable key for the userIds array
  const userIdsKey = memoizedUserIds.join(',');

  // Ref to track the current request to prevent race conditions
  const currentRequestRef = useRef<string>('');

  const loadUsers = useCallback(async (ids: string[]) => {
    if (ids.length === 0) {
      setUsers([]);
      return;
    }

    const requestKey = ids.join(',');
    currentRequestRef.current = requestKey;

    setIsLoading(true);
    setError(null);

    try {
      // Check cache first and separate cached vs uncached IDs
      const cachedUsers: User[] = [];
      const uncachedIds: string[] = [];

      for (const id of ids) {
        const cachedUser = userDetailsCache.get(id);
        if (cachedUser) {
          cachedUsers.push(cachedUser);
        } else {
          uncachedIds.push(id);
        }
      }

      // If all users are cached, return immediately
      if (uncachedIds.length === 0) {
        if (currentRequestRef.current === requestKey) {
          setUsers(cachedUsers);
          setIsLoading(false);
        }
        return;
      }

      // Fetch uncached users, reusing pending requests where possible
      const userPromises = uncachedIds.map(async (id) => {
        // Check if there's already a pending request for this user
        const existingRequest = pendingRequests.get(id);
        if (existingRequest) {
          return existingRequest;
        }

        // Create new request and cache the promise
        const request = fetch(`/api/users/search?q=${id}&limit=1`)
          .then(async (response) => {
            if (!response.ok) throw new Error('Failed to fetch user details');
            const data = await response.json();
            const user = data.users.find((u: User) => u.id === id);

            // Cache the result
            if (user) {
              userDetailsCache.set(id, user);
            }

            // Remove from pending requests
            pendingRequests.delete(id);

            return user || null;
          })
          .catch((err) => {
            // Remove from pending requests on error
            pendingRequests.delete(id);
            throw err;
          });

        pendingRequests.set(id, request);
        return request;
      });

      const userResults = await Promise.all(userPromises);
      const fetchedUsers = userResults.filter(Boolean) as User[];

      // Combine cached and fetched users, maintaining order
      const allUsers = ids
        .map((id) => {
          return (
            cachedUsers.find((u) => u.id === id) ||
            fetchedUsers.find((u) => u.id === id)
          );
        })
        .filter(Boolean) as User[];

      // Only update state if this is still the current request
      if (currentRequestRef.current === requestKey) {
        setUsers(allUsers);
      }
    } catch (err) {
      // Only update error state if this is still the current request
      if (currentRequestRef.current === requestKey) {
        const errorMessage =
          err instanceof Error ? err.message : 'An error occurred';
        setError(errorMessage);
        setUsers([]);
      }
    } finally {
      // Only update loading state if this is still the current request
      if (currentRequestRef.current === requestKey) {
        setIsLoading(false);
      }
    }
  }, []); // Empty dependency array - function is stable

  // Load users when userIds change, using memoized key
  useEffect(() => {
    loadUsers(memoizedUserIds);
  }, [userIdsKey, loadUsers]); // Use stable key instead of array

  return {
    users,
    isLoading,
    error,
    loadUsers,
  };
}
