'use client';

import {
  DynamicFileUpload,
  UploadedFile,
} from '@/features/shared/components/DynamicFileUpload';
import { DynamicRichTextEditor } from '@/features/shared/components/DynamicRichTextEditor';
import { ProfileAvatar } from '@/features/shared/components/ProfileAvatar';
import { Button } from '@/features/shared/components/ui/button';
import { useAuth } from '@/features/shared/hooks/useAuth';
import { Send } from 'lucide-react';
import { useRef } from 'react';
import { Ticket } from '../models/ticket.schema';

interface TicketReplyProps {
  ticket: Ticket;
  isSubmitting: boolean;
  replyContent: string;
  setReplyContent: (content: string) => void;
  uploadedFiles: UploadedFile[];
  setUploadedFiles: (files: UploadedFile[]) => void;
  handleReplySubmit: () => void;
  getReplyRecipientInfo: (
    ticket: Ticket,
    currentUserRole?: string
  ) => {
    name: string;
    email: string;
    avatar: string | undefined;
  };
  hasValidContent: (content: string) => boolean;
  countWords: (content: string) => number;
  getMinWordCount: (content: string) => number;
}

export function TicketReply({
  ticket,
  isSubmitting,
  replyContent,
  setReplyContent,
  uploadedFiles,
  setUploadedFiles,
  handleReplySubmit,
  getReplyRecipientInfo,
  hasValidContent,
  countWords,
  getMinWordCount,
}: TicketReplyProps) {
  const { role } = useAuth();
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleAttachClick = () => {
    fileInputRef.current?.click();
  };

  return (
    <div className='border-t border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800'>
      <div className='p-6'>
        <div className='space-y-4'>
          {(() => {
            const replyRecipient = getReplyRecipientInfo(ticket, role);
            return (
              <div className='flex items-center gap-3 text-sm text-gray-600 dark:text-gray-400'>
                <ProfileAvatar
                  avatarUrl={replyRecipient.avatar ?? null}
                  name={replyRecipient.name}
                  email={replyRecipient.email}
                  className='h-8 w-8'
                  fallbackClassName='bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 text-xs font-medium'
                />
                <div className='flex items-center gap-2'>
                  <span>Reply to:</span>
                  <span className='font-medium'>
                    {replyRecipient.name} ({replyRecipient.email})
                  </span>
                  <Button
                    variant='ghost'
                    size='sm'
                    className='h-6 w-6 p-0 ml-1 hover:bg-gray-100 dark:hover:bg-gray-700'
                  >
                    ×
                  </Button>
                </div>
              </div>
            );
          })()}
          <DynamicRichTextEditor
            value={replyContent}
            onChange={setReplyContent}
            placeholder='Type your reply...'
            className='min-h-24'
            disabled={isSubmitting}
            onAttachClick={handleAttachClick}
          />
          <DynamicFileUpload
            files={uploadedFiles}
            onFilesChange={setUploadedFiles}
            disabled={isSubmitting}
            fileInputRef={fileInputRef}
          />
          <div className='flex justify-end'>
            <Button
              onClick={handleReplySubmit}
              disabled={
                !hasValidContent(replyContent) ||
                isSubmitting ||
                countWords(replyContent) < getMinWordCount(replyContent)
              }
            >
              <Send className='h-4 w-4 mr-1' />
              {isSubmitting ? 'Sending...' : 'Send'}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
