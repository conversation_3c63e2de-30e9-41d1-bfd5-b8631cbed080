'use client';

import React, { useState, useCallback, useRef, useEffect } from 'react';
import { Slate, Editable } from 'slate-react';
import { cn } from '@/lib/utils';
import { RenderElementProps, RenderLeafProps } from 'slate-react';
import { useSlateEditor } from './useSlateEditor';
import { Toolbar } from './Toolbar';

interface RichTextEditorProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  className?: string;
  disabled?: boolean;
  onAttachClick?: () => void;
}

const DEFAULT_TEXT_COLOR = 'default';

export function RichTextEditor({
  value,
  onChange,
  placeholder = 'Type your message...',
  className,
  disabled = false,
  onAttachClick,
}: RichTextEditorProps) {
  const {
    editor,
    slateValue,
    handleValueChange,
    toggleMark,
    toggleBlock,
    currentTextColor,
    handleTextColorChange,
    resetTextColor,
  } = useSlateEditor(value, onChange);
  const [showTextColorPicker, setShowTextColorPicker] = useState(false);
  const colorPickerRef = useRef<HTMLDivElement>(null!);

  const handleKeyDown = useCallback(
    (event: React.KeyboardEvent) => {
      if (event.ctrlKey || event.metaKey) {
        switch (event.key) {
          case 'b':
            event.preventDefault();
            toggleMark('bold');
            break;
          case 'i':
            event.preventDefault();
            toggleMark('italic');
            break;
          case 'u':
            event.preventDefault();
            toggleMark('underline');
            break;
        }
      }
    },
    [toggleMark]
  );

  const renderElement = useCallback((props: RenderElementProps) => {
    const { attributes, children, element } = props;
    switch (element.type) {
      case 'heading-one':
        return <h1 {...attributes}>{children}</h1>;
      case 'heading-two':
        return <h2 {...attributes}>{children}</h2>;
      case 'block-quote':
        return <blockquote {...attributes}>{children}</blockquote>;
      case 'bulleted-list':
        return <ul {...attributes}>{children}</ul>;
      case 'numbered-list':
        return <ol {...attributes}>{children}</ol>;
      case 'list-item':
        return <li {...attributes}>{children}</li>;
      default:
        return <p {...attributes}>{children}</p>;
    }
  }, []);

  const renderLeaf = useCallback((props: RenderLeafProps) => {
    const { attributes, children, leaf } = props;
    let newChildren = children;
    if (leaf.bold) {
      newChildren = <strong>{newChildren}</strong>;
    }
    if (leaf.italic) {
      newChildren = <em>{newChildren}</em>;
    }
    if (leaf.underline) {
      newChildren = <u>{newChildren}</u>;
    }
    if (leaf.color && leaf.color !== DEFAULT_TEXT_COLOR) {
      newChildren = <span style={{ color: leaf.color }}>{newChildren}</span>;
    }
    return <span {...attributes}>{newChildren}</span>;
  }, []);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        colorPickerRef.current &&
        !colorPickerRef.current.contains(event.target as Node)
      ) {
        setShowTextColorPicker(false);
      }
    };
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [colorPickerRef]);

  return (
    <div className={cn('relative', className)} data-rich-text-editor='true'>
      <Slate
        editor={editor}
        initialValue={slateValue}
        onChange={handleValueChange}
      >
        <Toolbar
          editor={editor}
          disabled={disabled}
          onAttachClick={onAttachClick}
          toggleMark={toggleMark}
          toggleBlock={toggleBlock}
          currentTextColor={currentTextColor}
          handleTextColorChange={handleTextColorChange}
          resetTextColor={resetTextColor}
          showTextColorPicker={showTextColorPicker}
          setShowTextColorPicker={setShowTextColorPicker}
          colorPickerRef={colorPickerRef}
        />
        <div className='relative'>
          <Editable
            renderElement={renderElement}
            renderLeaf={renderLeaf}
            placeholder={placeholder}
            disabled={disabled}
            onKeyDown={handleKeyDown}
            className={cn(
              'file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input block w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none md:text-sm',
              'focus-visible:border-ring',
              'aria-invalid:border-destructive',
              'prose prose-sm max-w-none no-focus-ring text-foreground',
              '!min-h-[180px] h-auto',
              disabled && 'opacity-50 cursor-not-allowed pointer-events-none',
              '[&_p]:text-foreground [&_div]:text-foreground',
              '[&_strong]:text-foreground [&_em]:text-foreground [&_u]:text-foreground',
              '[&_h1]:text-foreground [&_h2]:text-foreground [&_h3]:text-foreground',
              '[&_li]:text-foreground [&_ul]:text-foreground [&_ol]:text-foreground',
              '[&_ul]:list-disc [&_ul]:pl-6 [&_ul]:ml-0 [&_ol]:list-decimal [&_ol]:pl-6 [&_ol]:ml-0',
              '[&_li]:mb-1 [&_li]:pl-1',
              '[&_h1]:text-2xl [&_h1]:font-bold [&_h1]:mb-4 [&_h1]:mt-2',
              '[&_h2]:text-xl [&_h2]:font-semibold [&_h2]:mb-3 [&_h2]:mt-2',
              '[&_p]:mt-0 [&_p:first-child]:mt-0 [&_p:not(:empty)]:mb-2 [&_p:not(:first-child)]:mb-2'
            )}
          />
        </div>
      </Slate>
    </div>
  );
}
