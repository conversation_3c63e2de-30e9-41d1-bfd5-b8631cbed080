'use client';

import { useTheme } from '@/features/shared/components/ThemeProvider';
import { useAuth } from '@/features/shared/hooks/useAuth';
import { useCallback, useEffect, useRef } from 'react';
import { AutoAssignmentRule } from '../models/settings.schema';
import {
  useSettingsBroadcast,
  useSettingsRealtime,
} from '../services/settings-realtime.service';
import { useSettingsStore } from '../store/use-settings-store';

// Type for department rules update (matches AdminSettingsUpdate schema)
type DepartmentRuleUpdate = {
  department: 'sales' | 'support' | 'marketing' | 'technical';
  assigned_agent_id: string | null;
  is_active: boolean;
  priority?: number;
};

/**
 * Hook for syncing settings with theme provider and handling real-time updates
 */
export function useSettingsSync() {
  const { tenantId, isSignedIn, user } = useAuth();
  const { setTheme } = useTheme();
  const syncIntervalRef = useRef<NodeJS.Timeout | null>(null);

  const { userSettings, loadSettings, lastSync, isLoading } =
    useSettingsStore();

  const userId = user?.id;

  // Set up real-time synchronization
  const realtimeStatus = useSettingsRealtime(tenantId || '', userId || '');

  // Set up cross-tab synchronization
  const { broadcastChange } = useSettingsBroadcast(
    tenantId || '',
    userId || ''
  );

  // Sync theme with settings
  useEffect(() => {
    if (userSettings?.theme_preference) {
      setTheme(userSettings.theme_preference);
    }
  }, [userSettings?.theme_preference, setTheme]);

  // Load settings on mount and tenant change
  useEffect(() => {
    if (isSignedIn && tenantId && userId && !isLoading) {
      loadSettings(tenantId, userId);
    }
  }, [isSignedIn, tenantId, userId, loadSettings]);

  // Set up periodic sync as fallback (reduced frequency due to real-time updates)
  useEffect(() => {
    if (!isSignedIn || !tenantId || !userId) {
      if (syncIntervalRef.current) {
        clearInterval(syncIntervalRef.current);
        syncIntervalRef.current = null;
      }
      return;
    }

    // Sync every 2 minutes if data is older than 10 minutes (fallback for real-time)
    syncIntervalRef.current = setInterval(() => {
      const now = Date.now();
      const tenMinutes = 10 * 60 * 1000;

      if (now - lastSync > tenMinutes) {
        loadSettings(tenantId, userId);
      }
    }, 120000); // 2 minutes

    return () => {
      if (syncIntervalRef.current) {
        clearInterval(syncIntervalRef.current);
        syncIntervalRef.current = null;
      }
    };
  }, [isSignedIn, tenantId, userId, lastSync, loadSettings]);

  // Manual refresh function
  const refreshSettings = useCallback(() => {
    if (tenantId && userId) {
      loadSettings(tenantId, userId);
    }
  }, [tenantId, userId, loadSettings]);

  return {
    refreshSettings,
    isLoading,
    userSettings,
    realtimeStatus,
    broadcastChange,
  };
}

/**
 * Hook for managing settings form state with optimistic updates
 */
export function useSettingsForm() {
  const { tenantId, user } = useAuth();
  const userId = user?.id;
  const {
    userSettings,
    adminSettings,
    updateUserSettings,
    updateAdminSettings,
    isLoading,
    error,
  } = useSettingsStore();

  const { broadcastChange } = useSettingsBroadcast(
    tenantId || '',
    userId || ''
  );

  const updateTheme = useCallback(
    async (theme: 'light' | 'dark' | 'system') => {
      await updateUserSettings(
        { theme_preference: theme },
        tenantId || undefined,
        userId || undefined
      );
      broadcastChange('user_settings', 'UPDATE');
    },
    [updateUserSettings, tenantId, userId, broadcastChange]
  );

  const updatePreferences = useCallback(
    async (preferences: Record<string, unknown>) => {
      await updateUserSettings(
        { preferences },
        tenantId || undefined,
        userId || undefined
      );
      broadcastChange('user_settings', 'UPDATE');
    },
    [updateUserSettings, tenantId, userId, broadcastChange]
  );

  const updateDefaultAgent = useCallback(
    async (agentId: string | null) => {
      await updateAdminSettings({ default_agent_id: agentId });
      broadcastChange('default_agent_settings', 'UPDATE');
    },
    [updateAdminSettings, broadcastChange]
  );

  const updateAutoAssignmentRules = useCallback(
    async (rules: AutoAssignmentRule[]) => {
      await updateAdminSettings({ auto_assignment_rules: rules });
      broadcastChange('auto_assignment_rules', 'UPDATE');
    },
    [updateAdminSettings, broadcastChange]
  );

  return {
    userSettings,
    adminSettings,
    isLoading,
    error,
    updateTheme,
    updatePreferences,
    updateDefaultAgent,
    updateAutoAssignmentRules,
  };
}

/**
 * Hook for settings UI state management
 */
export function useSettingsUI() {
  const {
    isSettingsOpen,
    activeSection,
    openSettings,
    closeSettings,
    setActiveSection,
  } = useSettingsStore();

  const openSection = useCallback(
    (section: string) => {
      if (isSettingsOpen) {
        setActiveSection(section);
      } else {
        openSettings(section);
      }
    },
    [isSettingsOpen, openSettings, setActiveSection]
  );

  return {
    isSettingsOpen,
    activeSection,
    openSettings,
    closeSettings,
    setActiveSection,
    openSection,
  };
}

/**
 * Hook for admin-specific settings functionality
 */
export function useAdminSettings() {
  const { isAdmin, isSuperAdmin } = useAuth();
  const { adminSettings, updateAdminSettings, isLoading } = useSettingsStore();

  const hasAdminAccess = isAdmin || isSuperAdmin;

  const updateDefaultAgent = useCallback(
    async (agentId: string | null) => {
      if (!hasAdminAccess) {
        throw new Error('Insufficient permissions');
      }
      await updateAdminSettings({ default_agent_id: agentId });
    },
    [hasAdminAccess, updateAdminSettings]
  );

  const updateDefaultAgentStatus = useCallback(
    async (agentId: string | null, isActive: boolean) => {
      if (!hasAdminAccess) {
        throw new Error('Insufficient permissions');
      }
      await updateAdminSettings({
        default_agent_id: agentId,
        default_agent_is_active: isActive,
      });
    },
    [hasAdminAccess, updateAdminSettings]
  );

  const updateDepartmentRules = useCallback(
    async (rules: DepartmentRuleUpdate[]) => {
      if (!hasAdminAccess) {
        throw new Error('Insufficient permissions');
      }
      await updateAdminSettings({ auto_assignment_rules: rules });
    },
    [hasAdminAccess, updateAdminSettings]
  );

  return {
    hasAdminAccess,
    adminSettings,
    updateDefaultAgent,
    updateDefaultAgentStatus,
    updateDepartmentRules,
    isLoading,
  };
}
