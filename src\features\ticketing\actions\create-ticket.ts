'use server';

import { z } from 'zod';
import { auth } from '@clerk/nextjs/server';
import { createServiceSupabaseClient } from '@/lib/supabase-server';
import { CreateTicketFormSchema } from '../models/ticket-form.schema';

export async function createTicket(
  formData: z.infer<typeof CreateTicketFormSchema>
) {
  const { userId } = await auth();
  if (!userId) {
    throw new Error('You must be logged in to create a ticket.');
  }

  const serviceSupabase = createServiceSupabaseClient();

  const { data: userData } = await serviceSupabase
    .from('users')
    .select('id, tenant_id')
    .eq('clerk_id', userId)
    .single();

  if (!userData) {
    throw new Error('User not found.');
  }

  const newTicket = {
    ...formData,
    tenant_id: userData.tenant_id,
    created_by: userData.id,
    status: 'new',
  };

  const { data, error } = await serviceSupabase
    .from('tickets')
    .insert(newTicket)
    .select()
    .single();

  if (error) {
    throw new Error(error.message);
  }

  return data;
}
