'use client';

import { useState } from 'react';
import { Badge } from '@/features/shared/components/ui/badge';
import { Button } from '@/features/shared/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/features/shared/components/ui/select';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/features/shared/components/ui/dialog';
import { cn } from '@/lib/utils';
import { Department } from '../models/ticket.schema';
import { usePermissions } from '@/features/shared/hooks/useAuth';
import { departmentConfig } from '../config/ticket-options';

interface ConfirmationDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  title: string;
  description: string;
  onConfirm: () => void;
}

function ConfirmationDialog({
  open,
  onOpenChange,
  title,
  description,
  onConfirm,
}: ConfirmationDialogProps) {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>{title}</DialogTitle>
          <DialogDescription>{description}</DialogDescription>
        </DialogHeader>
        <DialogFooter>
          <Button variant='outline' onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button onClick={onConfirm}>Confirm</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

interface InteractiveDepartmentBadgeProps {
  currentDepartment: Department;
  onDepartmentChange: (newDepartment: Department) => void;
}

export function InteractiveDepartmentBadge({
  currentDepartment,
  onDepartmentChange,
}: InteractiveDepartmentBadgeProps) {
  const { hasPermission } = usePermissions();
  const [confirmDialog, setConfirmDialog] = useState<{
    open: boolean;
    newDepartment: Department | null;
  }>({ open: false, newDepartment: null });

  // Use permission system only - department changes are restricted to admin/super_admin
  const canChangeDepartment = hasPermission('tickets.department.change');

  // If user doesn't have permission, render a static badge
  if (!canChangeDepartment) {
    return (
      <Badge
        className={cn(
          'text-xs transition-all duration-300 ease-in-out',
          departmentConfig[currentDepartment].color
        )}
      >
        {currentDepartment &&
          currentDepartment.charAt(0).toUpperCase() +
            currentDepartment.slice(1)}{' '}
        Department
      </Badge>
    );
  }

  const handleDepartmentSelect = (newDepartment: Department) => {
    if (newDepartment === currentDepartment) return;

    setConfirmDialog({
      open: true,
      newDepartment,
    });
  };

  const handleConfirm = () => {
    if (confirmDialog.newDepartment) {
      onDepartmentChange(confirmDialog.newDepartment);
    }
    setConfirmDialog({ open: false, newDepartment: null });
  };

  const currentOption = departmentConfig[currentDepartment];
  const newOption = confirmDialog.newDepartment
    ? departmentConfig[confirmDialog.newDepartment]
    : null;

  if (!canChangeDepartment) {
    return (
      <Badge
        className={cn('text-xs', departmentConfig[currentDepartment].color)}
      >
        {currentDepartment &&
          currentDepartment.charAt(0).toUpperCase() +
            currentDepartment.slice(1)}{' '}
        Department
      </Badge>
    );
  }

  return (
    <>
      <Select value={currentDepartment} onValueChange={handleDepartmentSelect}>
        <SelectTrigger
          className={cn(
            'inline-flex items-center justify-center rounded-md h-6! px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 gap-1 transition-[color,box-shadow] overflow-hidden',
            'border bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100',
            'hover:opacity-80 cursor-pointer'
          )}
        >
          <div
            className={cn(
              'w-1.5 h-1.5 mr-1 rounded-full',
              departmentConfig[currentDepartment].dotColor
            )}
          />
          <SelectValue>
            {currentDepartment &&
              currentDepartment.charAt(0).toUpperCase() +
                currentDepartment.slice(1)}{' '}
            Department
          </SelectValue>
        </SelectTrigger>
        <SelectContent>
          {Object.entries(departmentConfig).map(([value, config]) => (
            <SelectItem key={value} value={value}>
              <div className={cn('flex items-center gap-2 text-xs')}>
                <div
                  className={cn(
                    'w-1.5 h-1.5 mr-1 rounded-full',
                    config.dotColor
                  )}
                />
                {config.label}
              </div>
            </SelectItem>
          ))}
        </SelectContent>
      </Select>

      <ConfirmationDialog
        open={confirmDialog.open}
        onOpenChange={(open) => setConfirmDialog({ open, newDepartment: null })}
        title='Change Department'
        description={`Change department from ${currentOption?.label} to ${newOption?.label}?`}
        onConfirm={handleConfirm}
      />
    </>
  );
}
