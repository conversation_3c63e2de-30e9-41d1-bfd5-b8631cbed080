'use client';

import { useCallback, useRef } from 'react';
import { useTicketingStore } from '../store/use-ticketing-store';
import { cacheService } from '@/lib/cache/cache-service';

/**
 * Hook for intelligent cache warming of ticket messages
 * Preloads messages for tickets that are likely to be viewed
 */
export function useTicketCacheWarming() {
  const { currentTenantId } = useTicketingStore();
  const warmingInProgress = useRef(new Set<string>());

  // Warm cache for a specific ticket's messages
  const warmTicketMessages = useCallback(
    async (ticketId: string, priority: 'high' | 'low' = 'low') => {
      if (!currentTenantId || !ticketId) return;

      // Avoid duplicate warming requests
      if (warmingInProgress.current.has(ticketId)) {
        console.log(
          '🔥 Cache warming already in progress for ticket:',
          ticketId
        );
        return;
      }

      try {
        warmingInProgress.current.add(ticketId);

        console.log(
          `🔥 ${
            priority === 'high' ? 'Priority' : 'Background'
          } cache warming for ticket:`,
          ticketId
        );

        // Check if messages are already cached and fresh
        const cacheTimestamp = await cacheService.getMessageCacheTimestamp(
          currentTenantId,
          ticketId
        );
        const CACHE_FRESH_THRESHOLD = 5 * 60 * 1000; // 5 minutes

        if (cacheTimestamp > 0) {
          const cacheAge = Date.now() - cacheTimestamp;
          if (cacheAge < CACHE_FRESH_THRESHOLD) {
            console.log(
              '✅ Messages already cached and fresh for ticket:',
              ticketId
            );
            return;
          }
        }

        // Fetch messages from API and cache them
        const response = await fetch(
          `/api/tickets/${ticketId}/messages?tenant_id=${currentTenantId}`,
          {
            headers: {
              'Content-Type': 'application/json',
            },
            // Use lower priority for background requests
            ...(priority === 'low' && { priority: 'low' }),
          }
        );

        if (!response.ok) {
          console.warn('Failed to warm cache for ticket:', ticketId);
          return;
        }

        const data = await response.json();
        const messages = data.messages || [];

        // Cache messages even if empty to mark as cached
        await cacheService.cacheTicketMessages(
          currentTenantId,
          ticketId,
          messages
        );
        console.log(
          '💾 Warmed cache with',
          messages.length,
          'messages for ticket:',
          ticketId
        );
      } catch (error) {
        console.warn('Cache warming failed for ticket:', ticketId, error);
      } finally {
        warmingInProgress.current.delete(ticketId);
      }
    },
    [currentTenantId]
  );

  // Warm cache for multiple tickets (batch operation)
  const warmMultipleTickets = useCallback(
    async (ticketIds: string[], priority: 'high' | 'low' = 'low') => {
      if (!ticketIds.length) return;

      console.log('🔥 Batch cache warming for', ticketIds.length, 'tickets');

      // Process in small batches to avoid overwhelming the API
      const BATCH_SIZE = priority === 'high' ? 3 : 2;
      const batches = [];

      for (let i = 0; i < ticketIds.length; i += BATCH_SIZE) {
        batches.push(ticketIds.slice(i, i + BATCH_SIZE));
      }

      for (const batch of batches) {
        // Process batch in parallel
        await Promise.allSettled(
          batch.map((ticketId) => warmTicketMessages(ticketId, priority))
        );

        // Small delay between batches for low priority warming
        if (priority === 'low' && batches.length > 1) {
          await new Promise((resolve) => setTimeout(resolve, 100));
        }
      }
    },
    [warmTicketMessages]
  );

  // Smart warming based on user behavior patterns
  const smartWarm = useCallback(
    async (
      currentTicketId: string,
      allTickets: Array<{ id: string; status: string; priority: string }>
    ) => {
      if (!allTickets.length) return;

      // High priority: Current ticket (if not already warmed)
      await warmTicketMessages(currentTicketId, 'high');

      // Medium priority: Other open tickets from same user
      const openTickets = allTickets
        .filter(
          (ticket) =>
            ticket.id !== currentTicketId &&
            ['open', 'in_progress'].includes(ticket.status)
        )
        .slice(0, 5); // Limit to 5 most relevant

      if (openTickets.length > 0) {
        // Small delay to not interfere with current ticket loading
        setTimeout(() => {
          warmMultipleTickets(
            openTickets.map((t) => t.id),
            'low'
          );
        }, 500);
      }

      // Low priority: High priority tickets
      const highPriorityTickets = allTickets
        .filter(
          (ticket) =>
            ticket.id !== currentTicketId &&
            ticket.priority === 'high' &&
            !openTickets.some((ot) => ot.id === ticket.id)
        )
        .slice(0, 3);

      if (highPriorityTickets.length > 0) {
        setTimeout(() => {
          warmMultipleTickets(
            highPriorityTickets.map((t) => t.id),
            'low'
          );
        }, 2000);
      }
    },
    [warmTicketMessages, warmMultipleTickets]
  );

  // Clear warming state (useful for cleanup)
  const clearWarmingState = useCallback(() => {
    warmingInProgress.current.clear();
  }, []);

  return {
    warmTicketMessages,
    warmMultipleTickets,
    smartWarm,
    clearWarmingState,
    isWarming: (ticketId: string) => warmingInProgress.current.has(ticketId),
  };
}
