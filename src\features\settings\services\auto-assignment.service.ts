import { SupabaseClient } from '@supabase/supabase-js';
import { Department } from '../models/settings.schema';

interface AssignmentResult {
  assigned_agent_id: string | null;
  assignment_reason: 'department_rule' | 'default_agent' | 'none';
  rule_id?: string;
}

/**
 * Auto-assignment service for determining which agent should be assigned to new tickets
 */
export class AutoAssignmentService {
  constructor(private supabase: SupabaseClient) {}

  /**
   * Determines the agent to assign to a new ticket based on department rules and default settings
   */
  async getAssignmentForTicket(
    tenantId: string,
    department: Department
  ): Promise<AssignmentResult> {
    try {
      // First, check for department-specific rules
      const departmentAssignment = await this.getDepartmentAssignment(
        tenantId,
        department
      );

      if (departmentAssignment.assigned_agent_id) {
        return departmentAssignment;
      }

      // If no department rule, check for default agent
      const defaultAssignment = await this.getDefaultAssignment(tenantId);

      if (defaultAssignment.assigned_agent_id) {
        return defaultAssignment;
      }

      // No assignment rules found
      return {
        assigned_agent_id: null,
        assignment_reason: 'none',
      };
    } catch (error) {
      console.error('Auto-assignment error:', error);
      return {
        assigned_agent_id: null,
        assignment_reason: 'none',
      };
    }
  }

  /**
   * Gets department-specific assignment rule
   */
  private async getDepartmentAssignment(
    tenantId: string,
    department: Department
  ): Promise<AssignmentResult> {
    const { data: rules, error } = await this.supabase
      .from('auto_assignment_rules')
      .select('*')
      .eq('tenant_id', tenantId)
      .eq('department', department)
      .eq('is_active', true)
      .not('assigned_agent_id', 'is', null)
      .order('priority', { ascending: true })
      .limit(1);

    if (error) {
      console.error('Error fetching department assignment rules:', error);
      return {
        assigned_agent_id: null,
        assignment_reason: 'none',
      };
    }

    if (rules && rules.length > 0) {
      const rule = rules[0];

      // Verify the assigned agent is still active and has proper role
      const isAgentValid = await this.validateAgent(
        rule.assigned_agent_id,
        tenantId
      );

      if (isAgentValid) {
        return {
          assigned_agent_id: rule.assigned_agent_id,
          assignment_reason: 'department_rule',
          rule_id: rule.id,
        };
      }
    }

    return {
      assigned_agent_id: null,
      assignment_reason: 'none',
    };
  }

  /**
   * Gets default agent assignment
   */
  private async getDefaultAssignment(
    tenantId: string
  ): Promise<AssignmentResult> {
    const { data: settings, error } = await this.supabase
      .from('default_agent_settings')
      .select('*')
      .eq('tenant_id', tenantId)
      .eq('is_active', true)
      .not('default_agent_id', 'is', null)
      .limit(1);

    if (error) {
      console.error('Error fetching default agent settings:', error);
      return {
        assigned_agent_id: null,
        assignment_reason: 'none',
      };
    }

    if (settings && settings.length > 0) {
      const setting = settings[0];

      // Verify the default agent is still active and has proper role
      const isAgentValid = await this.validateAgent(
        setting.default_agent_id,
        tenantId
      );

      if (isAgentValid) {
        return {
          assigned_agent_id: setting.default_agent_id,
          assignment_reason: 'default_agent',
        };
      }
    }

    return {
      assigned_agent_id: null,
      assignment_reason: 'none',
    };
  }

  /**
   * Validates that an agent is still active and has the proper role
   */
  private async validateAgent(
    agentId: string,
    tenantId: string
  ): Promise<boolean> {
    const { data: agent, error } = await this.supabase
      .from('users')
      .select('id, role, status')
      .eq('id', agentId)
      .eq('tenant_id', tenantId)
      .single();

    if (error || !agent) {
      return false;
    }

    // Check if user is active and has admin or agent role
    return (
      agent.status === 'active' &&
      (agent.role === 'admin' || agent.role === 'agent')
    );
  }

  /**
   * Gets all active assignment rules for a tenant (for debugging/admin purposes)
   */
  async getActiveRules(tenantId: string) {
    const [departmentRules, defaultSettings] = await Promise.all([
      this.supabase
        .from('auto_assignment_rules')
        .select('*')
        .eq('tenant_id', tenantId)
        .eq('is_active', true)
        .order('priority', { ascending: true }),

      this.supabase
        .from('default_agent_settings')
        .select('*')
        .eq('tenant_id', tenantId)
        .eq('is_active', true),
    ]);

    return {
      departmentRules: departmentRules.data || [],
      defaultSettings: defaultSettings.data || [],
    };
  }

  /**
   * Creates assignment metadata for ticket creation
   */
  createAssignmentMetadata(
    result: AssignmentResult,
    assignedByUserId: string,
    assignedByClerkId: string,
    assignedByRole: string
  ) {
    if (!result.assigned_agent_id) {
      return null;
    }

    return {
      assigned_by: assignedByUserId,
      assigned_by_clerk_id: assignedByClerkId,
      assigned_at: new Date().toISOString(),
      assigned_by_role: assignedByRole,
      assignment_reason: result.assignment_reason,
      rule_id: result.rule_id,
      auto_assigned: true,
    };
  }
}
