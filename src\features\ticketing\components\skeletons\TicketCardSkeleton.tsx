'use client';

import { cn } from '@/lib/utils';
import {
  Skeleton,
  SkeletonText,
  SkeletonAvatar,
  SkeletonBadge,
} from '@/features/shared/components/ui/skeleton';

interface TicketCardSkeletonProps {
  isSelected?: boolean;
  hideStatus?: boolean;
  className?: string;
}

/**
 * Skeleton loader for TicketCard component
 * Matches the exact layout and structure of the actual TicketCard
 */
export function TicketCardSkeleton({
  isSelected = false,
  hideStatus = false,
  className,
}: TicketCardSkeletonProps) {
  return (
    <div
      className={cn(
        'p-4 border-b border-gray-100 dark:border-gray-700 transition-colors',
        !isSelected && 'hover:bg-gray-50 dark:hover:bg-gray-700',
        isSelected &&
          'bg-blue-50/60 dark:bg-blue-900/30 border-blue-200/50 dark:border-blue-600/50',
        className
      )}
    >
      <div className='flex items-start gap-3'>
        {/* Avatar Skeleton */}
        <SkeletonAvatar
          size='sm'
          className='shrink-0 border border-blue-200 dark:border-blue-600'
        />

        {/* Content Skeleton */}
        <div className='flex-1 min-w-0'>
          {/* Header Skeleton */}
          <div className='flex items-center justify-between mb-1'>
            <Skeleton className='h-4 w-24' />
            <Skeleton className='h-3 w-12 shrink-0 ml-2' />
          </div>

          {/* Message preview skeleton - 2 lines */}
          <div className='mb-2'>
            <SkeletonText lines={2} className='text-sm' />
          </div>

          {/* Footer with badges skeleton */}
          {!hideStatus && (
            <div className='flex items-center gap-2 flex-wrap'>
              <SkeletonBadge className='h-5 w-12' />
              <SkeletonBadge className='h-5 w-16' />
              <SkeletonBadge className='h-5 w-14' />
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

/**
 * Multiple ticket card skeletons for loading states
 */
export function TicketCardSkeletonList({
  count = 3,
  className,
}: {
  count?: number;
  className?: string;
}) {
  return (
    <div className={className}>
      {Array.from({ length: count }).map((_, index) => (
        <TicketCardSkeleton
          key={index}
          isSelected={index === 0} // First one appears selected
        />
      ))}
    </div>
  );
}
