import { useState, useCallback } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import {
  CreateTicketFormSchema,
  CreateTicketFormData,
} from '../models/ticket-form.schema';
import { useDraftPersistence } from './useDraftPersistence';
import { toast } from '@/features/shared/components/toast';
import { UploadedFile } from '@/features/shared/components/DynamicFileUpload';
import { createTicket } from '../actions/create-ticket';

/**
 * Hook for managing the create ticket form.
 *
 * @param tenantId - The ID of the current tenant.
 * @param onSubmit - The function to call when the form is submitted.
 * @returns An object with the form, state, and handlers.
 */
export function useCreateTicket(
  tenantId: string | null,
  _onSubmit: (
    data: CreateTicketFormData & { attachment_ids: string[] }
  ) => Promise<void>
) {
  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([]);
  const form = useForm<CreateTicketFormData>({
    resolver: zodResolver(CreateTicketFormSchema),
    defaultValues: {
      title: '',
      description: '',
      priority: 'high',
      department: 'marketing',
      assignedTo: undefined,
      cc: [],
    },
  });

  const { clearDraft } = useDraftPersistence(form);

  const handleSubmit = useCallback(
    async (data: CreateTicketFormData) => {
      if (!tenantId) {
        toast.error('Error', {
          description: 'Tenant information not available',
          duration: 4000,
        });
        return;
      }

      try {
        clearDraft();

        // TODO: Implement attachment handling
        // let attachmentIds: string[] = [];
        // if (uploadedFiles.length > 0) {
        //   attachmentIds = await FileUploadService.uploadFilesForTicket(
        //     uploadedFiles,
        //     tenantId
        //   );
        // }

        await createTicket(data);
        form.reset();
        setUploadedFiles([]);
      } catch (error) {
        console.error('Failed to create ticket:', error);
        toast.error('Failed to create ticket', {
          description: 'An unexpected error occurred. Please try again.',
        });
      }
    },
    [clearDraft, uploadedFiles, form, tenantId]
  );

  const handleDiscard = useCallback(() => {
    clearDraft();
    setUploadedFiles([]);
    form.reset();
  }, [clearDraft, form]);

  return {
    form,
    uploadedFiles,
    setUploadedFiles,
    handleSubmit,
    handleDiscard,
  };
}
