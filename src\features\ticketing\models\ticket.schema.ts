import { z } from 'zod';

export const TicketStatusSchema = z.enum([
  'new', // Assigned to agent but not yet opened
  'open', // Agent has opened the ticket
  'pending', // Waiting for response
  'resolved', // Issue resolved
  'closed', // Ticket closed
]);
export const TicketPrioritySchema = z.enum(['low', 'medium', 'high', 'urgent']);
export const DepartmentSchema = z.enum([
  'sales',
  'support',
  'marketing',
  'technical',
]);

export const AttachmentSchema = z.object({
  id: z.string(),
  name: z.string(),
  type: z.string(),
  size: z.number(),
  url: z.string(),
  uploadedAt: z.date(),
});

export const TicketMessageSchema = z.object({
  id: z.string(),
  content: z.string(),
  authorId: z.string(),
  authorName: z.string(),
  authorAvatar: z.string().optional(),
  createdAt: z.date(),
  attachments: z.array(AttachmentSchema).default([]),
});

export const AssignedUserSchema = z.object({
  id: z.string(),
  name: z.string(),
  email: z.string(),
  role: z.enum(['admin', 'agent', 'user', 'super_admin']),
});

export const TicketSchema = z.object({
  id: z.string(),
  tenantId: z.string(),
  title: z.string(),
  description: z.string(),
  status: TicketStatusSchema,
  priority: TicketPrioritySchema,
  department: DepartmentSchema,
  createdAt: z.date(),
  updatedAt: z.date(),
  userId: z.string(),
  creatorClerkId: z.string().optional(), // Clerk user ID of ticket creator (for frontend filtering)
  userName: z.string(),
  userEmail: z.string(),
  userAvatar: z.string().optional(),
  messages: z.array(TicketMessageSchema).default([]),
  attachments: z.array(AttachmentSchema).default([]),
  // Assignment tracking fields
  assignedTo: z.string().optional(), // User ID of assigned agent (database UUID)
  assignedToClerkId: z.string().optional(), // Clerk user ID of assigned agent (for frontend filtering)
  assignedBy: z.string().optional(), // User ID of admin who assigned (database UUID)
  assignedByClerkId: z.string().optional(), // Clerk user ID of admin who assigned (for frontend filtering)
  assignedAt: z.date().optional(), // When the assignment was made

  // Ticket opening tracking fields
  openedBy: z.string().optional(), // User ID of agent who opened the ticket
  openedAt: z.date().optional(), // When the ticket was opened by agent
  // Additional metadata fields from API
  dueDate: z.date().optional(),
  resolvedAt: z.date().optional(),
  closedAt: z.date().optional(),
  tags: z.array(z.string()).default([]),
  metadata: z.record(z.any()).default({}),
});

export type Ticket = z.infer<typeof TicketSchema>;
export type TicketStatus = z.infer<typeof TicketStatusSchema>;
export type TicketPriority = z.infer<typeof TicketPrioritySchema>;
export type Department = z.infer<typeof DepartmentSchema>;
export type Attachment = z.infer<typeof AttachmentSchema>;
export type TicketMessage = z.infer<typeof TicketMessageSchema>;
export type AssignedUser = z.infer<typeof AssignedUserSchema>;
