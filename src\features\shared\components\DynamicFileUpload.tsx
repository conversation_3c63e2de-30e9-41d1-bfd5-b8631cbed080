'use client';

/**
 * Dynamic FileUpload - Performance Optimized with Code Splitting
 *
 * This component provides a dynamically imported FileUpload to reduce
 * initial bundle size and improve page load performance.
 *
 * Key Features:
 * - Dynamic import with loading state
 * - SSR disabled for better performance
 * - Proper TypeScript support
 * - Fallback loading component
 *
 * <AUTHOR> Augster
 * @version 1.0 - Dynamic Import Optimized (January 2025)
 */

import dynamic from 'next/dynamic';
import { ComponentProps } from 'react';
import { FileUpload } from './FileUpload';

// Loading component for the file upload
const FileUploadLoading = () => (
  <div className='space-y-4'>
    <div className='flex items-center gap-2'>
      <div className='animate-pulse bg-gray-200 dark:bg-gray-700 h-10 w-32 rounded-md'></div>
      <div className='animate-pulse bg-gray-200 dark:bg-gray-700 h-4 w-24 rounded'></div>
    </div>
    <div className='text-center'>
      <div className='animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mx-auto mb-2'></div>
      <p className='text-sm text-gray-500 dark:text-gray-400'>
        Loading file upload...
      </p>
    </div>
  </div>
);

// Dynamic import of the FileUpload
const DynamicFileUpload = dynamic(
  () => import('./FileUpload').then((mod) => ({ default: mod.FileUpload })),
  {
    loading: FileUploadLoading,
    ssr: false, // Disable SSR for better performance
  }
);

// Export with proper TypeScript support
export type DynamicFileUploadProps = ComponentProps<typeof FileUpload>;
export type { UploadedFile } from './FileUpload';

export { DynamicFileUpload };
export default DynamicFileUpload;
