'use client';

import { useUser, useClerk, useOrganization } from '@clerk/nextjs';
import { useTenantStore } from '@/features/tenant/store/use-tenant-store';
import { useRouter } from 'next/navigation';
import { useCallback, useState, useMemo } from 'react';
import { Tenant } from '@/features/tenant/models/tenant.schema';
import { useTicketingSelectors } from '@/features/ticketing/store/use-ticketing-store';
import { cacheService } from '@/lib/cache/cache-service';

export interface AuthState {
  user: ReturnType<typeof useUser>['user'];
  isLoaded: boolean;
  isSignedIn: boolean;
  tenant: Tenant | null;
  tenantId: string | null;
  role: string;
  isSuperAdmin: boolean;
  isAdmin: boolean;
  isAgent: boolean;
  isUser: boolean;
  isTransitioning: boolean;
}

export interface AuthActions {
  signOut: () => Promise<void>;
  navigate: (path: '/tickets' | '/sign-in' | '/sign-up') => void;
}

/**
 * Optimized authentication hook with modern 2025 patterns
 */
export function useAuth(): AuthState & AuthActions;
export function useAuth<T>(
  selector: (state: AuthState & AuthActions) => T,
  _equals?: (a: T, b: T) => boolean
): T;
export function useAuth<T>(
  selector?: (state: AuthState & AuthActions) => T,
  _equals?: (a: T, b: T) => boolean
): T | (AuthState & AuthActions) {
  const { user, isLoaded, isSignedIn } = useUser();
  const { signOut: clerkSignOut } = useClerk();
  const { membership } = useOrganization();
  const { tenant, tenantId } = useTenantStore();
  const router = useRouter();
  const [isTransitioning, setIsTransitioning] = useState(false);

  // Get store actions for clearing state on logout
  const { clearCacheForTenant } = useTicketingSelectors.useCacheState();
  const { resetStore } = useTicketingSelectors.useTicketingActions();

  const role = useMemo(() => {
    if (!isSignedIn || !membership) {
      return 'user';
    }

    const clerkRole = membership.role;

    switch (clerkRole) {
      case 'org:super_admin':
        return 'super_admin';
      case 'org:admin':
        return 'admin';
      case 'org:agent':
        return 'agent';
      case 'org:member':
        return 'user';
      default:
        if (
          user?.emailAddresses?.[0]?.emailAddress === '<EMAIL>'
        ) {
          return 'super_admin';
        }
        return 'user';
    }
  }, [isSignedIn, membership, user?.emailAddresses]);

  const isSuperAdmin = role === 'super_admin';
  const isAdmin = role === 'admin' || isSuperAdmin;
  const isAgent = role === 'agent';
  const isUser = role === 'user';

  const signOut = useCallback(async () => {
    setIsTransitioning(true);
    try {
      // Clear all cached data and state before signing out
      if (tenantId) {
        console.log('🗑️ Clearing cache and state for tenant:', tenantId);
        await clearCacheForTenant(tenantId);
      }

      // Clear all cache data (for cases where tenantId might be null)
      await cacheService.clearAllCache();

      // Reset the Zustand store to initial state
      resetStore();

      // Sign out from Clerk
      await clerkSignOut();
      router.replace('/sign-in');
    } catch (error) {
      console.error('Error during logout:', error);
      // Force redirect even if cache clearing fails
      window.location.href = '/sign-in';
    } finally {
      setIsTransitioning(false);
    }
  }, [clerkSignOut, router, tenantId, clearCacheForTenant, resetStore]);

  const navigate = useCallback(
    (path: '/tickets' | '/sign-in' | '/sign-up') => {
      setIsTransitioning(true);
      router.push(path);
      setTimeout(() => setIsTransitioning(false), 100);
    },
    [router]
  );

  const state = {
    user,
    isLoaded,
    isSignedIn: isSignedIn || false,
    tenant,
    tenantId,
    role,
    isSuperAdmin,
    isAdmin,
    isAgent,
    isUser,
    isTransitioning,
    signOut,
    navigate,
  };

  return selector ? selector(state) : state;
}

/**
 * Hook for checking if user has specific permissions
 */
export function usePermissions() {
  const { isSuperAdmin, isAdmin, isAgent, isUser, tenant } = useAuth();

  const hasPermission = useCallback(
    (permission: string): boolean => {
      if (isSuperAdmin || isAdmin) return true;

      const tenantFeatures = tenant?.settings?.features || [];

      switch (permission) {
        case 'tickets.view':
          return isUser || isAgent;
        case 'tickets.create':
          return isSuperAdmin || isAdmin || isUser;
        case 'tickets.update':
          return isAgent || isAdmin || isSuperAdmin;
        case 'tickets.delete':
          return isAdmin || isSuperAdmin;
        case 'tickets.assign':
          return isAdmin || isSuperAdmin;
        case 'tickets.priority.change':
          return isAdmin || isSuperAdmin;
        case 'tickets.department.change':
          return isAdmin || isSuperAdmin;
        case 'analytics.view':
          return (
            (isAgent || isAdmin || isSuperAdmin) &&
            tenantFeatures.includes('analytics')
          );
        case 'integrations.manage':
          return (
            (isAdmin || isSuperAdmin) && tenantFeatures.includes('integrations')
          );
        case 'users.manage':
          return isAdmin || isSuperAdmin;
        case 'settings.manage':
          return isAdmin || isSuperAdmin;
        default:
          return false;
      }
    },
    [isSuperAdmin, isAdmin, isAgent, isUser, tenant]
  );

  const canAccessFeature = (feature: string): boolean => {
    const tenantFeatures = tenant?.settings?.features || [];
    return tenantFeatures.includes(feature);
  };

  return {
    hasPermission,
    canAccessFeature,
    isSuperAdmin,
    isAdmin,
    isAgent,
    isUser,
  };
}
