'use client';

import { Badge } from '@/features/shared/components/ui/badge';
import { Button } from '@/features/shared/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/features/shared/components/ui/select';
import { Play } from 'lucide-react';
import { cn } from '@/lib/utils';
import { format } from 'date-fns';
import { Ticket, TicketPriority, Department } from '../models/ticket.schema';
import { useAuth } from '@/features/shared/hooks/useAuth';
import { InteractivePriorityBadge } from './InteractivePriorityBadge';
import { InteractiveDepartmentBadge } from './InteractiveDepartmentBadge';
import { statusColors } from '../config/ticket-options';

interface TicketDetailHeaderProps {
  ticket: Ticket;
  isReplyDisabled: boolean;
  isOpeningTicket: boolean;
  handleOpenTicketInDetail: () => void;
  handlePriorityChange: (newPriority: TicketPriority) => void;
  handleDepartmentChange: (newDepartment: Department) => void;
}

export function TicketDetailHeader({
  ticket,
  isReplyDisabled,
  isOpeningTicket,
  handleOpenTicketInDetail,
  handlePriorityChange,
  handleDepartmentChange,
}: TicketDetailHeaderProps) {
  const { role } = useAuth();

  return (
    <div className='p-6 border-b border-gray-200 dark:border-gray-700 shrink-0'>
      <div className='flex items-center justify-between mb-4'>
        <h1 className='text-xl font-semibold text-gray-900 dark:text-gray-100'>
          {ticket.title}
        </h1>
        <div className='flex gap-2 transition-all duration-300 ease-in-out'>
          {isReplyDisabled ? (
            <Button
              onClick={handleOpenTicketInDetail}
              disabled={isOpeningTicket}
              className='bg-blue-600 hover:bg-blue-700 text-white dark:bg-blue-600 dark:hover:bg-blue-700 disabled:opacity-50 transition-all duration-200 ease-in-out'
            >
              <Play className='h-4 w-4 mr-2 transition-transform duration-200' />
              {isOpeningTicket ? 'Opening...' : 'Open This Ticket'}
            </Button>
          ) : (
            <Select defaultValue='ticket-actions'>
              <SelectTrigger className='w-40 transition-all duration-300 ease-in-out'>
                <SelectValue placeholder='Ticket actions' />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value='ticket-actions'>Ticket actions</SelectItem>
                <SelectItem value='close'>Close ticket</SelectItem>
                <SelectItem value='assign'>Assign to agent</SelectItem>
              </SelectContent>
            </Select>
          )}
        </div>
      </div>
      <div className='flex flex-wrap gap-2 mb-3'>
        {ticket.status && (
          <Badge
            className={cn(
              'text-xs transition-all duration-300 ease-in-out',
              statusColors[ticket.status]
            )}
          >
            {ticket.status.charAt(0).toUpperCase() + ticket.status.slice(1)}
          </Badge>
        )}
        <InteractivePriorityBadge
          currentPriority={ticket.priority}
          onPriorityChange={handlePriorityChange}
        />
        <InteractiveDepartmentBadge
          currentDepartment={ticket.department}
          onDepartmentChange={handleDepartmentChange}
        />
      </div>

      {ticket.assignedTo && (
        <div className='flex flex-wrap gap-4 text-sm text-gray-600 dark:text-gray-400'>
          {role === 'agent' && (
            <div className='flex items-center gap-2'>
              <span className='font-medium'>
                {ticket.metadata?.assignment?.auto_assigned
                  ? 'Auto-assigned by:'
                  : 'Assigned by:'}
              </span>
              <span>
                {ticket.metadata?.assignment?.assignedByUser?.name ||
                  ticket.metadata?.createdByUser?.name ||
                  'System'}
              </span>
              <Badge variant='outline' className='text-xs'>
                {ticket.metadata?.assignment?.assignedByUser?.role ||
                  ticket.metadata?.createdByUser?.role ||
                  'system'}
              </Badge>
              {ticket.metadata?.assignment?.auto_assigned && (
                <Badge
                  variant='secondary'
                  className='text-xs bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300'
                >
                  {ticket.metadata.assignment.assignment_reason ===
                  'department_rule'
                    ? 'Dept. Rule'
                    : 'Default'}
                </Badge>
              )}
              {ticket.assignedAt && (
                <span className='text-xs text-gray-500'>
                  on {format(new Date(ticket.assignedAt), 'MMM d, yyyy')}
                </span>
              )}
            </div>
          )}
          {(role === 'admin' || role === 'super_admin') &&
            ticket.metadata?.assignedUser && (
              <div className='flex items-center gap-2'>
                <span className='font-medium'>Assigned to:</span>
                <span>{ticket.metadata.assignedUser.name}</span>
                <Badge variant='outline' className='text-xs'>
                  {ticket.metadata.assignedUser.role}
                </Badge>
                {ticket.metadata?.assignment?.auto_assigned && (
                  <Badge
                    variant='secondary'
                    className='text-xs bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300'
                  >
                    Auto-assigned (
                    {ticket.metadata.assignment.assignment_reason ===
                    'department_rule'
                      ? 'Dept. Rule'
                      : 'Default'}
                    )
                  </Badge>
                )}
                {ticket.assignedAt && (
                  <span className='text-xs text-gray-500'>
                    on {format(new Date(ticket.assignedAt), 'MMM d, yyyy')}
                  </span>
                )}
              </div>
            )}
        </div>
      )}
    </div>
  );
}
