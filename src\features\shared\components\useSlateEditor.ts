import { useState, useMemo, useCallback, useEffect } from 'react';
import {
  createE<PERSON><PERSON>,
  Editor,
  Transforms,
  Element as SlateElement,
  Range,
} from 'slate';
import { withReact, ReactEditor } from 'slate-react';
import {
  CustomText,
  SlateValue,
  isBlockActive,
  isMarkActive,
} from './slate-types';
import { slateToHtml, htmlToSlate } from './slate-html-converter';

const DEFAULT_TEXT_COLOR = 'default';

export function useSlateEditor(
  initialValue: string,
  onChange: (value: string) => void
) {
  const editor = useMemo(() => withReact(createEditor()), []);
  const [slateValue, setSlateValue] = useState<SlateValue>(() =>
    htmlToSlate(initialValue)
  );
  const [currentTextColor, setCurrentTextColor] =
    useState<string>(DEFAULT_TEXT_COLOR);

  const handleValueChange = useCallback(
    (newValue: SlateValue) => {
      try {
        // Validate the new value structure before setting it
        if (!newValue || !Array.isArray(newValue) || newValue.length === 0) {
          console.warn('Invalid Slate value detected, using fallback');
          const fallbackValue: SlateValue = [
            { type: 'paragraph', children: [{ text: '' }] },
          ];
          setSlateValue(fallbackValue);
          onChange('');
          return;
        }

        setSlateValue(newValue);
        const htmlValue = slateToHtml(newValue);
        onChange(htmlValue);
      } catch (error) {
        console.error('Error in handleValueChange:', error);
        // Fallback to safe state
        const fallbackValue: SlateValue = [
          { type: 'paragraph', children: [{ text: '' }] },
        ];
        setSlateValue(fallbackValue);
        onChange('');
      }
    },
    [onChange]
  );

  const toggleMark = useCallback(
    (format: keyof Omit<CustomText, 'text'>) => {
      try {
        const isActive = isMarkActive(editor, format);
        if (isActive) {
          Editor.removeMark(editor, format);
        } else {
          Editor.addMark(editor, format, true);
        }
        ReactEditor.focus(editor);
      } catch (error) {
        console.error('Error in toggleMark:', error);
        // Attempt to focus editor safely
        try {
          ReactEditor.focus(editor);
        } catch (focusError) {
          console.warn('Could not focus editor after toggleMark error');
        }
      }
    },
    [editor]
  );

  const toggleBlock = useCallback(
    (format: string) => {
      try {
        const isActive = isBlockActive(editor, format);
        const isList = ['numbered-list', 'bulleted-list'].includes(format);

        Transforms.unwrapNodes(editor, {
          match: (n) =>
            !Editor.isEditor(n) &&
            SlateElement.isElement(n) &&
            ['numbered-list', 'bulleted-list'].includes(n.type),
          split: true,
        });

        const newProperties: Partial<SlateElement> = {
          type: isActive
            ? 'paragraph'
            : isList
              ? 'list-item'
              : (format as SlateElement['type']),
        };
        Transforms.setNodes<SlateElement>(editor, newProperties);

        if (!isActive && isList) {
          const block = {
            type: format as 'numbered-list' | 'bulleted-list',
            children: [],
          };
          Transforms.wrapNodes(editor, block);
        }
        ReactEditor.focus(editor);
      } catch (error) {
        console.error('Error in toggleBlock:', error);
        // Attempt to focus editor safely
        try {
          ReactEditor.focus(editor);
        } catch (focusError) {
          console.warn('Could not focus editor after toggleBlock error');
        }
      }
    },
    [editor]
  );

  const handleTextColorChange = useCallback(
    (color: string) => {
      ReactEditor.focus(editor);
      if (editor.selection && !Range.isCollapsed(editor.selection)) {
        Editor.addMark(editor, 'color', color);
      }
      setCurrentTextColor(color);
      Editor.addMark(editor, 'color', color);
    },
    [editor]
  );

  const resetTextColor = useCallback(() => {
    ReactEditor.focus(editor);
    Editor.removeMark(editor, 'color');
    setCurrentTextColor(DEFAULT_TEXT_COLOR);
  }, [editor]);

  useEffect(() => {
    const newSlateValue = htmlToSlate(initialValue);
    setSlateValue(newSlateValue);
    editor.children = newSlateValue;
    editor.onChange();
  }, [initialValue, editor]);

  return {
    editor,
    slateValue,
    handleValueChange,
    toggleMark,
    toggleBlock,
    currentTextColor,
    handleTextColorChange,
    resetTextColor,
  };
}
