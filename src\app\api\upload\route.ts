import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { createServiceSupabaseClient } from '@/lib/supabase-server';

const ALLOWED_FILE_TYPES = [
  'application/pdf',
  'image/jpeg',
  'image/jpg',
  'image/png',
  'image/gif',
  'application/msword',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  'text/plain',
];

const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB

async function getTenantUuid(
  serviceSupabase: ReturnType<typeof createServiceSupabaseClient>,
  tenantParam: string
): Promise<string> {
  // If it's already a UUID, return it
  if (tenantParam.includes('-')) {
    return tenantParam;
  }

  // Otherwise, look up by subdomain
  const { data, error } = await serviceSupabase
    .from('tenants')
    .select('id')
    .eq('subdomain', tenantParam)
    .single();

  if (error || !data) {
    throw new Error('Tenant not found');
  }

  return data.id;
}

async function validateUserAccess(
  serviceSupabase: ReturnType<typeof createServiceSupabaseClient>,
  userId: string,
  tenantUuid: string
) {
  const { data: user, error } = await serviceSupabase
    .from('users')
    .select('id, tenant_id, status')
    .eq('clerk_id', userId)
    .eq('tenant_id', tenantUuid)
    .single();

  if (error || !user) {
    throw new Error('User not found or access denied');
  }

  return user;
}

export async function POST(request: NextRequest) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const formData = await request.formData();
    const file = formData.get('file') as File;
    const tenantId = formData.get('tenant_id') as string;
    const ticketId = formData.get('ticket_id') as string | null;
    const messageId = formData.get('message_id') as string | null;

    if (!file) {
      return NextResponse.json({ error: 'No file provided' }, { status: 400 });
    }

    if (!tenantId) {
      return NextResponse.json(
        { error: 'Tenant ID is required' },
        { status: 400 }
      );
    }

    // Validate file type
    if (!ALLOWED_FILE_TYPES.includes(file.type)) {
      return NextResponse.json(
        {
          error:
            'Only pdf, jpg, jpeg, png, gif, doc, docx, txt files are allowed.',
        },
        { status: 400 }
      );
    }

    // Validate file size
    if (file.size > MAX_FILE_SIZE) {
      return NextResponse.json(
        { error: 'File size must be under 10MB.' },
        { status: 400 }
      );
    }

    const serviceSupabase = createServiceSupabaseClient();
    const tenantUuid = await getTenantUuid(serviceSupabase, tenantId);
    const userData = await validateUserAccess(
      serviceSupabase,
      userId,
      tenantUuid
    );

    console.log('Upload details:', {
      fileName: file.name,
      fileSize: file.size,
      fileType: file.type,
      tenantUuid,
      userId: userData.id,
    });

    // Generate unique file path
    const timestamp = Date.now();
    const randomId = Math.random().toString(36).substring(2, 11);
    const fileExtension = file.name.split('.').pop() || 'bin';
    const fileName = `${timestamp}_${randomId}.${fileExtension}`;
    const storagePath = `${tenantUuid}/attachments/${fileName}`;

    console.log('Storage path:', storagePath);

    // Upload file to Supabase storage
    const fileBuffer = await file.arrayBuffer();
    const { error: uploadError } = await serviceSupabase.storage
      .from('attachments')
      .upload(storagePath, fileBuffer, {
        contentType: file.type,
        upsert: false,
      });

    if (uploadError) {
      console.error('File upload error:', uploadError);
      return NextResponse.json(
        { error: `Failed to upload file: ${uploadError.message}` },
        { status: 500 }
      );
    }

    // Get public URL for the uploaded file
    const { data: urlData } = serviceSupabase.storage
      .from('attachments')
      .getPublicUrl(storagePath);

    // Save attachment metadata to database
    const attachmentData = {
      tenant_id: tenantUuid,
      ticket_id: ticketId,
      message_id: messageId,
      file_name: file.name,
      file_type: file.type,
      file_size: file.size,
      storage_bucket: 'attachments',
      storage_path: storagePath,
      uploaded_by: userData.id,
      is_public: false,
      metadata: {
        original_name: file.name,
        upload_timestamp: timestamp,
      },
    };

    const { data: attachment, error: dbError } = await serviceSupabase
      .from('attachments')
      .insert(attachmentData)
      .select('*')
      .single();

    if (dbError) {
      console.error('Database error:', dbError);
      // Clean up uploaded file if database insert fails
      await serviceSupabase.storage.from('attachments').remove([storagePath]);

      return NextResponse.json(
        { error: `Failed to save attachment metadata: ${dbError.message}` },
        { status: 500 }
      );
    }

    return NextResponse.json(
      {
        id: attachment.id,
        name: attachment.file_name,
        type: attachment.file_type,
        size: attachment.file_size,
        url: urlData.publicUrl,
        uploadedAt: attachment.created_at,
      },
      { status: 201 }
    );
  } catch (error) {
    console.error('Upload API error:', error);
    const errorMessage =
      error instanceof Error ? error.message : 'Unknown error';
    return NextResponse.json(
      { error: `Internal server error: ${errorMessage}` },
      { status: 500 }
    );
  }
}
