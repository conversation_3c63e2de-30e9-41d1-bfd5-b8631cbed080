import { SupabaseClient } from '@supabase/supabase-js';
import { Database } from '@/types/supabase';
import { Ticket } from '@/features/ticketing/models/ticket.schema';
import { cacheService } from '@/lib/cache/cache-service';

type TicketRow = Database['public']['Tables']['tickets']['Row'];

class RealtimeDataService {
  private supabase: SupabaseClient<Database>;

  constructor(supabase: SupabaseClient<Database>) {
    this.supabase = supabase;
  }

  private async getTenantSubdomain(tenantUuid: string): Promise<string> {
    const cached = await cacheService.getTenantSubdomain(tenantUuid);
    if (cached) return cached;

    const { data, error } = await this.supabase
      .from('tenants')
      .select('subdomain')
      .eq('id', tenantUuid)
      .single();

    if (error || !data) {
      await cacheService.setTenantSubdomain(tenantUuid, tenantUuid);
      return tenantUuid;
    }

    await cacheService.setTenantSubdomain(tenantUuid, data.subdomain);
    return data.subdomain;
  }

  private async getUserInfo(
    userId: string
  ): Promise<{ name: string; email: string }> {
    const cached = await cacheService.getUserInfo(userId);
    if (cached) return cached;

    const { data, error } = await this.supabase
      .from('users')
      .select('first_name, last_name, email')
      .eq('id', userId)
      .single();

    if (error || !data) {
      const fallback = { name: 'Unknown User', email: '<EMAIL>' };
      await cacheService.setUserInfo(userId, fallback);
      return fallback;
    }

    const userInfo = {
      name:
        `${data.first_name || ''} ${data.last_name || ''}`.trim() ||
        'Unknown User',
      email: data.email || '<EMAIL>',
    };

    await cacheService.setUserInfo(userId, userInfo);
    return userInfo;
  }

  public async transformTicketRow(payload: TicketRow): Promise<Ticket> {
    const [tenantId, userInfo] = await Promise.all([
      this.getTenantSubdomain(payload.tenant_id),
      this.getUserInfo(payload.created_by),
    ]);

    return {
      id: payload.id,
      tenantId,
      title: payload.title,
      description: payload.description || '',
      status: payload.status as 'open' | 'closed' | 'resolved' | 'pending',
      priority: payload.priority as 'low' | 'medium' | 'high' | 'urgent',
      department: payload.department as
        | 'sales'
        | 'support'
        | 'marketing'
        | 'technical',
      userId: payload.created_by,
      userName: userInfo.name,
      userEmail: userInfo.email,
      userAvatar: undefined,
      createdAt: new Date(payload.created_at || new Date()),
      updatedAt: new Date(payload.updated_at || new Date()),
      messages: [],
      attachments: [],
      assignedTo: payload.assigned_to || undefined,
      assignedBy: payload.assigned_to ? payload.created_by : undefined,
      assignedAt: payload.assigned_to
        ? new Date(payload.updated_at || new Date())
        : undefined,
      dueDate: payload.due_date ? new Date(payload.due_date) : undefined,
      resolvedAt: payload.resolved_at
        ? new Date(payload.resolved_at)
        : undefined,
      closedAt: payload.closed_at ? new Date(payload.closed_at) : undefined,
      tags: payload.tags || [],
      metadata: (payload.metadata && typeof payload.metadata === 'object'
        ? (payload.metadata as Record<string, unknown>)
        : {}) as Record<string, unknown>,
    };
  }
}

export default RealtimeDataService;
