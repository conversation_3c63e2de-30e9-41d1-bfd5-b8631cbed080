import { cacheDB, CachedTicket, CachedResponse, CachedUser } from './dexie-db';
import { Ticket } from '@/features/ticketing/models/ticket.schema';

// Type definitions for API responses
interface ResponseData {
  id: string;
  author_id: string;
  content: string;
  message_type?: 'reply' | 'note' | 'status_change';
  is_internal?: boolean;
  created_at: string;
}

interface UserData {
  id: string;
  email: string;
  first_name?: string;
  last_name?: string;
  role: string;
  status: string;
}

interface TicketMessageData {
  id: string;
  author_id: string;
  content: string;
  message_type?: 'message' | 'note' | 'status_change';
  is_internal?: boolean;
  created_at: string;
  updated_at?: string;
  author?: {
    id: string;
    first_name: string;
    last_name: string;
    email: string;
  };
}

export class CacheService {
  private static instance: CacheService;
  private supabase: unknown = null;

  private constructor() {}

  static getInstance(): CacheService {
    if (!CacheService.instance) {
      CacheService.instance = new CacheService();
    }
    return CacheService.instance;
  }

  // Initialize with Supabase client
  initialize(supabaseClient: unknown) {
    this.supabase = supabaseClient;
  }

  // Ticket operations
  async cacheTickets(tenantId: string, tickets: Ticket[]): Promise<void> {
    const cachedTickets: CachedTicket[] = tickets.map((ticket) => ({
      ...ticket,
      tenant_id: tenantId,
      cached_at: Date.now(),
      version: 1,
    }));

    await cacheDB.tickets.bulkPut(cachedTickets);
    await cacheDB.updateLastSync(tenantId, 'tickets', Date.now());
  }

  async getCachedTickets(tenantId: string): Promise<CachedTicket[]> {
    return cacheDB.getTicketsForTenant(tenantId);
  }

  async addTicketToCache(tenantId: string, ticket: Ticket): Promise<void> {
    const cachedTicket: CachedTicket = {
      ...ticket,
      tenant_id: tenantId,
      cached_at: Date.now(),
      version: 1,
    };

    await cacheDB.tickets.put(cachedTicket);
  }

  async updateTicketInCache(
    tenantId: string,
    ticketId: string,
    updates: Partial<Ticket>
  ): Promise<void> {
    const existingTicket = await cacheDB.tickets
      .where(['tenant_id', 'id'])
      .equals([tenantId, ticketId])
      .first();

    if (existingTicket) {
      const updatedTicket: CachedTicket = {
        ...existingTicket,
        ...updates,
        cached_at: Date.now(),
        version: existingTicket.version + 1,
      };

      await cacheDB.tickets.put(updatedTicket);
    }
  }

  // Response operations
  async cacheResponses(
    tenantId: string,
    ticketId: string,
    responses: ResponseData[]
  ): Promise<void> {
    const cachedResponses: CachedResponse[] = responses.map((response) => ({
      id: response.id,
      tenant_id: tenantId,
      ticket_id: ticketId,
      author_id: response.author_id,
      content: response.content,
      message_type: response.message_type || 'reply',
      is_internal: response.is_internal || false,
      created_at: response.created_at,
      updated_at: response.created_at, // Use created_at as fallback for updated_at
      cached_at: Date.now(),
      version: 1,
    }));

    await cacheDB.responses.bulkPut(cachedResponses);
    await cacheDB.updateLastSync(tenantId, 'responses', Date.now());
  }

  async getCachedResponses(
    tenantId: string,
    ticketId: string
  ): Promise<CachedResponse[]> {
    return cacheDB.getResponsesForTicket(tenantId, ticketId);
  }

  // Enhanced message operations for ticket messages
  async cacheTicketMessages(
    tenantId: string,
    ticketId: string,
    messages: TicketMessageData[]
  ): Promise<void> {
    console.log(
      '💾 Caching',
      messages.length,
      'messages for ticket:',
      ticketId
    );

    const cachedMessages: CachedResponse[] = messages.map((message) => {
      const baseMessage: CachedResponse = {
        id: message.id,
        tenant_id: tenantId,
        ticket_id: ticketId,
        author_id: message.author_id,
        content: message.content,
        message_type: (message.message_type || 'message') as
          | 'reply'
          | 'note'
          | 'status_change'
          | 'message',
        is_internal: message.is_internal || false,
        created_at: message.created_at,
        updated_at: message.updated_at || message.created_at,
        cached_at: Date.now(),
        version: 1,
      };

      // Add optional fields only if they exist
      if (message.author?.first_name) {
        baseMessage.author_first_name = message.author.first_name;
      }
      if (message.author?.last_name) {
        baseMessage.author_last_name = message.author.last_name;
      }
      if (message.author?.email) {
        baseMessage.author_email = message.author.email;
      }

      return baseMessage;
    });

    await cacheDB.responses.bulkPut(cachedMessages);
    await cacheDB.updateLastSync(tenantId, `messages_${ticketId}`, Date.now());
  }

  async getCachedTicketMessages(
    tenantId: string,
    ticketId: string
  ): Promise<CachedResponse[]> {
    console.log('🔍 Retrieving cached messages for ticket:', ticketId);
    return cacheDB.getResponsesForTicket(tenantId, ticketId);
  }

  async addMessageToCache(
    tenantId: string,
    ticketId: string,
    message: TicketMessageData
  ): Promise<void> {
    console.log('➕ Adding single message to cache:', message.id);

    const cachedMessage: CachedResponse = {
      id: message.id,
      tenant_id: tenantId,
      ticket_id: ticketId,
      author_id: message.author_id,
      content: message.content,
      message_type: (message.message_type || 'message') as
        | 'reply'
        | 'note'
        | 'status_change'
        | 'message',
      is_internal: message.is_internal || false,
      created_at: message.created_at,
      updated_at: message.updated_at || message.created_at,
      cached_at: Date.now(),
      version: 1,
    };

    // Add optional fields only if they exist
    if (message.author?.first_name) {
      cachedMessage.author_first_name = message.author.first_name;
    }
    if (message.author?.last_name) {
      cachedMessage.author_last_name = message.author.last_name;
    }
    if (message.author?.email) {
      cachedMessage.author_email = message.author.email;
    }

    await cacheDB.responses.put(cachedMessage);
  }

  async updateMessageInCache(
    tenantId: string,
    messageId: string,
    updates: Partial<TicketMessageData>
  ): Promise<void> {
    console.log('🔧 Updating message in cache:', messageId);

    const existingMessage = await cacheDB.responses
      .where(['tenant_id', 'id'])
      .equals([tenantId, messageId])
      .first();

    if (existingMessage) {
      const updatedMessage: CachedResponse = {
        ...existingMessage,
        ...updates,
        cached_at: Date.now(),
        version: existingMessage.version + 1,
      };

      await cacheDB.responses.put(updatedMessage);
    }
  }

  async getMessageCacheTimestamp(
    tenantId: string,
    ticketId: string
  ): Promise<number> {
    return cacheDB.getLastSync(tenantId, `messages_${ticketId}`);
  }

  async clearMessagesForTicket(
    tenantId: string,
    ticketId: string
  ): Promise<void> {
    console.log('🗑️ Clearing cached messages for ticket:', ticketId);
    await cacheDB.responses
      .where(['tenant_id', 'ticket_id'])
      .equals([tenantId, ticketId])
      .delete();
  }

  // Clear all cache (useful for schema changes)
  async clearAllCache(): Promise<void> {
    console.log('🗑️ Clearing all cache data due to schema change');
    await Promise.all([
      cacheDB.tickets.clear(),
      cacheDB.responses.clear(),
      cacheDB.users.clear(),
      cacheDB.metadata.clear(),
    ]);
  }

  // Cache management and optimization
  async getCacheStats(tenantId: string): Promise<{
    ticketCount: number;
    messageCount: number;
    userCount: number;
    totalSize: number;
    oldestEntry: number;
    newestEntry: number;
  }> {
    const [tickets, messages, users] = await Promise.all([
      cacheDB.getTicketsForTenant(tenantId),
      cacheDB.responses.where('tenant_id').equals(tenantId).toArray(),
      cacheDB.getUsersForTenant(tenantId),
    ]);

    const allEntries = [
      ...tickets.map((t) => t.cached_at),
      ...messages.map((m) => m.cached_at),
      ...users.map((u) => u.cached_at),
    ];

    return {
      ticketCount: tickets.length,
      messageCount: messages.length,
      userCount: users.length,
      totalSize: tickets.length + messages.length + users.length,
      oldestEntry: allEntries.length > 0 ? Math.min(...allEntries) : 0,
      newestEntry: allEntries.length > 0 ? Math.max(...allEntries) : 0,
    };
  }

  async cleanupOldCache(
    tenantId: string,
    maxAgeMs: number = 24 * 60 * 60 * 1000
  ): Promise<void> {
    const cutoffTime = Date.now() - maxAgeMs;
    console.log(
      '🧹 Cleaning up cache entries older than',
      new Date(cutoffTime)
    );

    const [deletedTickets, deletedMessages, deletedUsers] = await Promise.all([
      cacheDB.tickets
        .where('tenant_id')
        .equals(tenantId)
        .and((item) => item.cached_at < cutoffTime)
        .delete(),
      cacheDB.responses
        .where('tenant_id')
        .equals(tenantId)
        .and((item) => item.cached_at < cutoffTime)
        .delete(),
      cacheDB.users
        .where('tenant_id')
        .equals(tenantId)
        .and((item) => item.cached_at < cutoffTime)
        .delete(),
    ]);

    console.log(
      `🧹 Cleaned up ${
        deletedTickets + deletedMessages + deletedUsers
      } old cache entries`
    );
  }

  async limitCacheSize(
    tenantId: string,
    maxEntries: number = 1000
  ): Promise<void> {
    const stats = await this.getCacheStats(tenantId);

    if (stats.totalSize <= maxEntries) {
      return;
    }

    console.log('📏 Cache size limit exceeded, cleaning up oldest entries');

    // Remove oldest messages first (they're most likely to be stale)
    const oldMessages = await cacheDB.responses
      .orderBy('cached_at')
      .filter((response) => response.tenant_id === tenantId)
      .limit(Math.max(0, stats.messageCount - Math.floor(maxEntries * 0.6)))
      .toArray();

    if (oldMessages.length > 0) {
      await cacheDB.responses.bulkDelete(oldMessages.map((m) => m.id));
      console.log(
        '🗑️ Removed',
        oldMessages.length,
        'old message cache entries'
      );
    }

    // Remove oldest tickets if still over limit
    const remainingStats = await this.getCacheStats(tenantId);
    if (remainingStats.totalSize > maxEntries) {
      const oldTickets = await cacheDB.tickets
        .orderBy('cached_at')
        .filter((ticket) => ticket.tenant_id === tenantId)
        .limit(
          Math.max(0, remainingStats.ticketCount - Math.floor(maxEntries * 0.3))
        )
        .toArray();

      if (oldTickets.length > 0) {
        await cacheDB.tickets.bulkDelete(oldTickets.map((t) => t.id));
        console.log(
          '🗑️ Removed',
          oldTickets.length,
          'old ticket cache entries'
        );
      }
    }
  }

  // User operations
  async cacheUsers(tenantId: string, users: UserData[]): Promise<void> {
    const cachedUsers: CachedUser[] = users.map((user) => ({
      id: user.id,
      tenant_id: tenantId,
      email: user.email,
      first_name: user.first_name || '',
      last_name: user.last_name || '',
      role: user.role,
      status: user.status,
      cached_at: Date.now(),
      version: 1,
    }));

    await cacheDB.users.bulkPut(cachedUsers);
    await cacheDB.updateLastSync(tenantId, 'users', Date.now());
  }

  async getCachedUsers(tenantId: string): Promise<CachedUser[]> {
    return cacheDB.getUsersForTenant(tenantId);
  }

  async getTenantSubdomain(tenantUuid: string): Promise<string | null> {
    const metadata = await cacheDB.metadata.get(`tenant_${tenantUuid}`);
    return metadata?.value || null;
  }

  async setTenantSubdomain(
    tenantUuid: string,
    subdomain: string
  ): Promise<void> {
    await cacheDB.metadata.put({
      id: `tenant_${tenantUuid}`,
      value: subdomain,
      cached_at: Date.now(),
    });
  }

  async getUserInfo(
    userId: string
  ): Promise<{ name: string; email: string } | null> {
    const user = await cacheDB.users.get(userId);
    if (!user) return null;
    return {
      name: `${user.first_name} ${user.last_name}`,
      email: user.email,
    };
  }

  async setUserInfo(
    userId: string,
    userInfo: { name: string; email: string }
  ): Promise<void> {
    const [firstName, ...lastNameParts] = userInfo.name.split(' ');
    const lastName = lastNameParts.join(' ');
    await cacheDB.users.put({
      id: userId,
      email: userInfo.email || '',
      first_name: firstName || '',
      last_name: lastName || '',
      tenant_id: '', // This is not ideal, but we don't have tenant info here
      role: '',
      status: '',
      cached_at: Date.now(),
      version: 1,
    });
  }

  // Delta sync operations
  async performDeltaSync(tenantId: string): Promise<void> {
    if (!this.supabase) {
      console.warn('Supabase client not initialized for cache service');
      return;
    }

    try {
      // Get last sync timestamps
      const ticketsLastSync = await cacheDB.getLastSync(tenantId, 'tickets');
      const usersLastSync = await cacheDB.getLastSync(tenantId, 'users');

      // Sync tickets
      if (ticketsLastSync > 0) {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const { data: updatedTickets } = await (this.supabase as any)
          .from('tickets')
          .select('*')
          .eq('tenant_id', tenantId)
          .gte('updated_at', new Date(ticketsLastSync).toISOString());

        if (updatedTickets && updatedTickets.length > 0) {
          await this.cacheTickets(
            tenantId,
            updatedTickets as unknown as Ticket[]
          );
        }
      }

      // Sync users
      if (usersLastSync > 0) {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const { data: updatedUsers } = await (this.supabase as any)
          .from('users')
          .select('*')
          .eq('tenant_id', tenantId)
          .gte('updated_at', new Date(usersLastSync).toISOString());

        if (updatedUsers && updatedUsers.length > 0) {
          await this.cacheUsers(
            tenantId,
            updatedUsers as unknown as UserData[]
          );
        }
      }
    } catch (error) {
      console.error('Delta sync failed:', error);
    }
  }

  // Cache management
  async clearCache(tenantId: string): Promise<void> {
    await cacheDB.clearTenantCache(tenantId);
  }
}

export const cacheService = CacheService.getInstance();
