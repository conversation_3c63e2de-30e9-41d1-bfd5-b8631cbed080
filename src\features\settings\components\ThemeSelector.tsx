'use client';

import { useState } from 'react';

import { useTheme } from '@/features/shared/components/ThemeProvider';
import { Card } from '@/features/shared/components/ui/card';
import { cn } from '@/lib/utils';
import { Check, Monitor, Moon, Sun } from 'lucide-react';
import { toast } from 'sonner';
import { useSettingsForm } from '../hooks/useSettingsSync';

type Theme = 'light' | 'dark' | 'system';

interface ThemeOption {
  value: Theme;
  label: string;
  description: string;
  icon: React.ComponentType<{ className?: string }>;
}

const themeOptions: ThemeOption[] = [
  {
    value: 'light',
    label: 'Light',
    description: 'Clean and bright interface',
    icon: Sun,
  },
  {
    value: 'dark',
    label: 'Dark',
    description: 'Easy on the eyes in low light',
    icon: Moon,
  },
  {
    value: 'system',
    label: 'System',
    description: 'Follows your device settings',
    icon: Monitor,
  },
];

export function ThemeSelector() {
  const { theme } = useTheme();
  const { updateTheme, isLoading } = useSettingsForm();
  const [selectedTheme, setSelectedTheme] = useState<Theme>(theme);

  const handleThemeChange = async (newTheme: Theme) => {
    if (newTheme === selectedTheme) return;

    setSelectedTheme(newTheme);

    try {
      await updateTheme(newTheme);
      toast.success(`Theme changed to ${newTheme}`);
    } catch (error) {
      // Revert selection on error
      setSelectedTheme(theme);
      toast.error('Failed to update theme preference');
      console.error('Theme update error:', error);
    }
  };

  return (
    <div className='space-y-4'>
      <div className='grid grid-cols-1 sm:grid-cols-3 gap-3'>
        {themeOptions.map((option) => {
          const Icon = option.icon;
          const isSelected = selectedTheme === option.value;
          const isCurrentTheme = theme === option.value;

          return (
            <Card
              key={option.value}
              className={cn(
                'relative cursor-pointer transition-all duration-200 hover:shadow-md',
                'border-2 p-4 bg-white dark:bg-gray-800',
                isSelected
                  ? 'border-primary'
                  : 'border-gray-200 dark:border-gray-700 hover:border-primary/50'
              )}
              onClick={() => handleThemeChange(option.value)}
            >
              {/* Selection indicator */}
              {isSelected && (
                <div className='absolute top-2 right-2'>
                  <div className='flex items-center justify-center w-5 h-5 bg-primary rounded-full'>
                    <Check className='w-3 h-3 text-primary-foreground' />
                  </div>
                </div>
              )}

              <div className='flex flex-col items-center text-center space-y-3'>
                {/* Theme preview */}
                <div
                  className={cn(
                    'w-16 h-12 rounded-lg border-2 flex items-center justify-center',
                    option.value === 'light' && 'bg-white border-gray-300',
                    option.value === 'dark' && 'bg-gray-900 border-gray-700',
                    option.value === 'system' &&
                      'bg-gradient-to-r from-white to-gray-900 border-gray-400'
                  )}
                >
                  <Icon
                    className={cn(
                      'w-6 h-6',
                      option.value === 'light' && 'text-gray-700',
                      option.value === 'dark' && 'text-gray-300',
                      option.value === 'system' && 'text-white'
                    )}
                  />
                </div>

                {/* Theme info */}
                <div className='space-y-1 text-center'>
                  <div className='flex items-center justify-center gap-2'>
                    <h3
                      className={cn(
                        'font-medium text-sm',
                        isSelected ? 'text-primary' : 'text-foreground'
                      )}
                    >
                      {option.label}
                    </h3>
                    {isCurrentTheme && (
                      <div
                        className='w-2 h-2 bg-green-500 rounded-full'
                        title='Currently active'
                      />
                    )}
                  </div>
                  <p className='text-xs text-muted-foreground text-center'>
                    {option.description}
                  </p>
                </div>
              </div>
            </Card>
          );
        })}
      </div>

      {/* Additional info */}
      <div className='text-xs text-muted-foreground space-y-1'>
        <p>
          • <strong>Light:</strong> Best for daytime use and well-lit
          environments
        </p>
        <p>
          • <strong>Dark:</strong> Reduces eye strain in low-light conditions
        </p>
        <p>
          • <strong>System:</strong> Automatically switches based on your
          device&apos;s settings
        </p>
      </div>

      {/* Loading state */}
      {isLoading && (
        <div className='flex items-center gap-2 text-sm text-muted-foreground'>
          <div className='w-4 h-4 border-2 border-primary border-t-transparent rounded-full animate-spin' />
          Updating theme preference...
        </div>
      )}
    </div>
  );
}
