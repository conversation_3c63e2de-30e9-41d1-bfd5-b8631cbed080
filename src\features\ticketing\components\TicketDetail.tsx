'use client';

import {
  DynamicFileUpload,
  UploadedFile,
} from '@/features/shared/components/DynamicFileUpload';
import { DynamicRichTextEditor } from '@/features/shared/components/DynamicRichTextEditor';
import { ProfileAvatar } from '@/features/shared/components/ProfileAvatar';
import { toast } from '@/features/shared/components/toast';
import { Button } from '@/features/shared/components/ui/button';
import { useAuth } from '@/features/shared/hooks/useAuth';
import { FileUploadService } from '@/lib/services/file-upload.service';
import { cn } from '@/lib/utils';
import { Send } from 'lucide-react';
import { useCallback, useLayoutEffect, useRef, useState } from 'react';
import { useOpenTicket } from '../hooks/useOpenTicket';
import { useTicketCacheWarming } from '../hooks/useTicketCacheWarming';
import { useTicketMessages } from '../hooks/useTicketMessages';
import {
  Attachment,
  Department,
  Ticket,
  TicketPriority,
} from '../models/ticket.schema';
import {
  useTicketingSelectors,
  useTicketingStore,
} from '../store/use-ticketing-store';
import { TicketDetailSkeleton } from './skeletons/TicketDetailSkeleton';

import { MessageItem } from './MessageItem';

/**
 * Count words in text content, stripping HTML tags
 */
function countWords(text: string): number {
  if (!text || typeof text !== 'string') return 0;

  // Strip HTML tags and decode HTML entities
  const strippedText = text
    .replace(/<[^>]*>/g, ' ') // Remove HTML tags
    .replace(/&[^;]+;/g, ' ') // Remove HTML entities
    .replace(/\s+/g, ' ') // Normalize whitespace
    .trim();

  if (!strippedText) return 0;

  // Split by whitespace and filter out empty strings
  return strippedText.split(/\s+/).filter((word) => word.length > 0).length;
}

/**
 * Check if content has meaningful text (including list content)
 */
function hasValidContent(content: string): boolean {
  if (!content || typeof content !== 'string') return false;

  // Check if content is just empty HTML tags
  const strippedContent = content
    .replace(/<p[^>]*>\s*<\/p>/gi, '') // Remove empty paragraphs
    .replace(/<br\s*\/?>/gi, '') // Remove line breaks
    .replace(/&nbsp;/g, ' ') // Replace non-breaking spaces
    .replace(/<[^>]*>/g, ' ') // Remove all HTML tags
    .replace(/\s+/g, ' ') // Normalize whitespace
    .trim();

  return strippedContent.length > 0;
}

/**
 * Get minimum word count for content validation
 * Lists require fewer words since they're structured content
 */
function getMinWordCount(content: string): number {
  if (!content) return 5;

  // If content contains lists, require fewer words
  if (content.includes('<ul>') || content.includes('<ol>')) {
    return 2; // Lists need only 2 words minimum
  }

  return 5; // Regular content needs 5 words
}

interface TicketDetailProps {
  ticket: Ticket;
  onTicketUpdate?: (ticketId: string, updates: Partial<Ticket>) => void;
}

/**
 * TicketDetail Component - Enhanced with Safe HTML Rendering
 *
 * This component properly displays ticket content from both data sources
 * with safe HTML rendering to prevent XSS attacks while preserving formatting:
 * - Mock data: ticket.messages[0].content (legacy structure)
 * - Real API data: ticket.description (current structure)
 *
 * Key Features:
 * - Safe HTML rendering with DOMPurify sanitization
 * - Support for both data structures (mock and real API)
 * - XSS protection for user-generated content
 * - Proper HTML formatting display
 *
 * <AUTHOR> Augster
 * @version 2.2 - Safe HTML Rendering (January 2025)
 */
type DisplayMessage = {
  id: string;
  author: {
    name: string;
    email?: string;
    avatarUrl?: string;
  };
  content: string;
  createdAt: Date;
  attachments?: Attachment[];
  isInternal?: boolean;
  isOptimistic?: boolean;
};

// Helper function to determine reply recipient information
function getReplyRecipientInfo(ticket: Ticket, currentUserRole?: string) {
  const isAgent = currentUserRole === 'agent';
  const isAdmin =
    currentUserRole === 'admin' || currentUserRole === 'super_admin';

  const createdByAdmin =
    ticket.metadata?.createdByUser?.role === 'admin' ||
    ticket.metadata?.createdByUser?.role === 'super_admin';

  if (isAgent) {
    return createdByAdmin
      ? {
          name: ticket.metadata.createdByUser.name,
          email: ticket.metadata.createdByUser.email,
          avatar: undefined,
        }
      : {
          name: ticket.userName,
          email: ticket.userEmail,
          avatar: ticket.userAvatar,
        };
  }

  if (isAdmin) {
    return ticket.assignedTo && ticket.metadata?.assignedUser
      ? {
          name: ticket.metadata.assignedUser.name,
          email: ticket.metadata.assignedUser.email,
          avatar: undefined,
        }
      : {
          name: ticket.userName,
          email: ticket.userEmail,
          avatar: ticket.userAvatar,
        };
  }

  // Default for user
  return {
    name: 'Support Team',
    email: '<EMAIL>',
    avatar: undefined,
  };
}

import { TicketDetailHeader } from './TicketDetailHeader';

export function TicketDetail({ ticket, onTicketUpdate }: TicketDetailProps) {
  const [replyContent, setReplyContent] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([]);

  const fileInputRef = useRef<HTMLInputElement>(null);

  console.log(
    '🎫 TicketDetail component mounted for ticket:',
    ticket.id,
    ticket.title
  );

  const { user, role } = useAuth();

  // Create user info object for optimistic message creation
  const currentUserInfo = user
    ? {
        id: user.id,
        firstName: user.firstName || '',
        lastName: user.lastName || '',
        email: user.emailAddresses?.[0]?.emailAddress || '',
      }
    : undefined;

  const {
    messages,
    sendMessage,
    isLoading: isLoadingMessages,
  } = useTicketMessages(ticket.id, currentUserInfo);
  const { smartWarm } = useTicketCacheWarming();
  const { currentTenantId } = useTicketingStore();
  const { updateTicketOptimistic } = useTicketingSelectors.useOptimisticState();
  const { openTicket } = useOpenTicket();

  // Determine if reply functionality should be disabled
  // For agents: disable when ticket status is 'new' (not yet opened)
  const isReplyDisabled = role === 'agent' && ticket.status === 'new';

  // State for opening ticket
  const [isOpeningTicket, setIsOpeningTicket] = useState(false);

  // Handle opening ticket in detail view
  const handleOpenTicketInDetail = useCallback(async () => {
    if (isOpeningTicket) return;

    setIsOpeningTicket(true);
    try {
      // Apply optimistic update immediately for instant UI feedback
      updateTicketOptimistic(ticket.id, {
        status: 'open' as const,
        openedBy: user?.id,
        openedAt: new Date(),
      });

      const result = await openTicket(ticket.id);
      if (result.success) {
        // Success! The optimistic update is already applied
        // Real-time sync will ensure data consistency across all clients
        toast.success('Ticket Opened', {
          description: 'You can now reply to this ticket.',
        });
      } else {
        // Revert optimistic update on failure
        updateTicketOptimistic(ticket.id, {
          status: 'new' as const,
          openedBy: undefined,
          openedAt: undefined,
        });
        console.error('Failed to open ticket:', result.error);
        toast.error('Failed to Open Ticket', {
          description: result.error || 'An unexpected error occurred.',
        });
      }
    } catch (error) {
      // Revert optimistic update on error
      updateTicketOptimistic(ticket.id, {
        status: 'new' as const,
        openedBy: undefined,
        openedAt: undefined,
      });
      console.error('Error opening ticket:', error);
      toast.error('Error Opening Ticket', {
        description: 'An unexpected error occurred. Please try again.',
      });
    } finally {
      setIsOpeningTicket(false);
    }
  }, [
    ticket.id,
    openTicket,
    isOpeningTicket,
    updateTicketOptimistic,
    user?.id,
  ]);

  const handlePriorityChange = (newPriority: TicketPriority) => {
    onTicketUpdate?.(ticket.id, { priority: newPriority });
  };

  const handleDepartmentChange = (newDepartment: Department) => {
    onTicketUpdate?.(ticket.id, { department: newDepartment });
  };

  const handleAttachClick = () => {
    fileInputRef.current?.click();
  };

  const handleReplySubmit = async () => {
    const minWords = getMinWordCount(replyContent);
    const wordCount = countWords(replyContent);
    const hasContent = hasValidContent(replyContent);

    if (!hasContent || isSubmitting || wordCount < minWords) return;

    if (!currentTenantId) {
      toast.error('Error', {
        description: 'Tenant information not available',
        duration: 4000,
      });
      return;
    }

    try {
      setIsSubmitting(true);

      // Upload files first if any
      let attachmentIds: string[] = [];
      if (uploadedFiles.length > 0) {
        try {
          attachmentIds = await FileUploadService.uploadFilesForMessage(
            uploadedFiles,
            currentTenantId,
            ticket.id
          );
        } catch (uploadError) {
          console.error('Failed to upload files:', uploadError);
          toast.error('Upload Failed', {
            description: 'Failed to upload attachments. Please try again.',
            duration: 4000,
          });
          return;
        }
      }

      // Send message with attachment IDs and uploaded files for optimistic UI
      const uploadedFilesForOptimistic = uploadedFiles.map((f) => ({
        file: f.file,
        id: f.id,
      }));
      await sendMessage(
        replyContent,
        attachmentIds,
        'message',
        uploadedFilesForOptimistic
      );

      // Clear the reply content and files
      setReplyContent('');
      setUploadedFiles([]);

      // Show success toast
      toast.success('Reply Sent', {
        description: 'Your reply has been sent successfully',
        duration: 3000,
      });
    } catch (error) {
      console.error('Failed to send reply:', error);
      toast.error('Failed to Send Reply', {
        description:
          'Please try again or contact support if the problem persists',
        duration: 4000,
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Smart cache warming when ticket is viewed
  useLayoutEffect(() => {
    // Trigger smart cache warming for related tickets
    // This runs after the component is mounted and messages are loading
    const timer = setTimeout(() => {
      // Mock ticket data for smart warming - in real app this would come from store
      const mockRelatedTickets = [
        { id: ticket.id, status: ticket.status, priority: ticket.priority },
      ];
      smartWarm(ticket.id, mockRelatedTickets);
    }, 100); // Small delay to not interfere with initial message loading

    return () => clearTimeout(timer);
  }, [ticket.id, ticket.status, ticket.priority, smartWarm]);

  const mainMessageContent =
    ticket.messages?.[0]?.content || ticket.description;
  const mainMessageAttachments =
    ticket.messages?.[0]?.attachments || ticket.attachments;

  const allMessages: DisplayMessage[] = [];
  if (mainMessageContent) {
    allMessages.push({
      id: 'original-ticket',
      author: {
        name: ticket.userName,
        email: ticket.userEmail,
        ...(ticket.userAvatar && { avatarUrl: ticket.userAvatar }),
      },
      content: mainMessageContent,
      createdAt: ticket.createdAt,
      attachments: mainMessageAttachments,
      isInternal: false,
    });
  }
  messages.forEach((msg) => {
    const authorName = `${msg.author?.first_name || ''} ${
      msg.author?.last_name || ''
    }`.trim();

    // Transform attachments to match the Attachment interface
    const messageAttachments: Attachment[] = (msg.attachments || []).map(
      (att) => ({
        id: att.id,
        name: att.file_name,
        type: att.file_type,
        size: att.file_size,
        url: `/api/attachments/${att.id}`, // We'll need to create this endpoint
        uploadedAt: new Date(att.created_at),
      })
    );

    allMessages.push({
      id: msg.id,
      author: {
        name: authorName,
        ...(msg.author?.email && { email: msg.author.email }),
        ...(msg.author?.avatar_url && { avatarUrl: msg.author.avatar_url }),
      },
      content: msg.content,
      createdAt: new Date(msg.created_at),
      attachments: messageAttachments,
      isInternal: msg.is_internal,
    });
  });

  // Show skeleton loading during message loading
  if (isLoadingMessages) {
    return <TicketDetailSkeleton />;
  }

  return (
    <div className='flex-1 h-[calc(100%-3rem)] my-6 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm flex flex-col overflow-hidden'>
      <TicketDetailHeader
        ticket={ticket}
        isReplyDisabled={isReplyDisabled}
        isOpeningTicket={isOpeningTicket}
        handleOpenTicketInDetail={handleOpenTicketInDetail}
        handlePriorityChange={handlePriorityChange}
        handleDepartmentChange={handleDepartmentChange}
      />
      {/* Scrollable Content Area */}
      <div className='flex-1 overflow-y-auto min-h-0'>
        <div className='space-y-0'>
          {allMessages.map((message, index) => (
            <div
              key={`${ticket.id}-${message.id}`}
              className={cn(
                index > 0 && 'border-t border-gray-200 dark:border-gray-700'
              )}
            >
              <MessageItem
                message={message}
                isInitiallyExpanded={index === allMessages.length - 1}
                isLastMessage={index === allMessages.length - 1}
                currentUserEmail={user?.emailAddresses[0]?.emailAddress}
                ticket={ticket}
              />
            </div>
          ))}
        </div>

        {/* Reply section - Hidden for agents when ticket status is 'new' */}
        {!isReplyDisabled && (
          <div className='border-t border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800'>
            <div className='p-6'>
              <div className='space-y-4'>
                {(() => {
                  const replyRecipient = getReplyRecipientInfo(ticket, role);
                  return (
                    <div className='flex items-center gap-3 text-sm text-gray-600 dark:text-gray-400'>
                      <ProfileAvatar
                        avatarUrl={replyRecipient.avatar ?? null}
                        name={replyRecipient.name}
                        email={replyRecipient.email}
                        className='h-8 w-8'
                        fallbackClassName='bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 text-xs font-medium'
                      />
                      <div className='flex items-center gap-2'>
                        <span>Reply to:</span>
                        <span className='font-medium'>
                          {replyRecipient.name} ({replyRecipient.email})
                        </span>
                        <Button
                          variant='ghost'
                          size='sm'
                          className='h-6 w-6 p-0 ml-1 hover:bg-gray-100 dark:hover:bg-gray-700'
                        >
                          ×
                        </Button>
                      </div>
                    </div>
                  );
                })()}
                <DynamicRichTextEditor
                  value={replyContent}
                  onChange={setReplyContent}
                  placeholder='Type your reply...'
                  className='min-h-24'
                  disabled={isSubmitting}
                  onAttachClick={handleAttachClick}
                />
                <DynamicFileUpload
                  files={uploadedFiles}
                  onFilesChange={setUploadedFiles}
                  disabled={isSubmitting}
                  fileInputRef={fileInputRef}
                />
                <div className='flex justify-end'>
                  <Button
                    onClick={handleReplySubmit}
                    disabled={
                      !hasValidContent(replyContent) ||
                      isSubmitting ||
                      countWords(replyContent) < getMinWordCount(replyContent)
                    }
                  >
                    <Send className='h-4 w-4 mr-1' />
                    {isSubmitting ? 'Sending...' : 'Send'}
                  </Button>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Message for agents when ticket is in 'new' status */}
        {isReplyDisabled && (
          <div className='border-t border-gray-200 dark:border-gray-700 bg-amber-50 dark:bg-amber-900/20'>
            <div className='p-6 text-center'>
              <div className='text-amber-600 dark:text-amber-400 text-sm font-medium mb-2'>
                Ticket Not Yet Opened
              </div>
              <div className='text-amber-700 dark:text-amber-300 text-sm'>
                You must open this ticket before you can reply to it. Click
                &quot;Open This Ticket&quot; above to begin working on this
                ticket.
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
