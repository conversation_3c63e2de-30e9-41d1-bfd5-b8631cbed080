'use client';

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/features/shared/components/ui/card';
import {
  Avatar,
  AvatarFallback,
  AvatarImage,
} from '@/features/shared/components/ui/avatar';
import { useAuth } from '@/features/shared/hooks/useAuth';

export function ProfileSection() {
  const { user } = useAuth();

  const userName =
    user?.firstName && user?.lastName
      ? `${user.firstName} ${user.lastName}`
      : user?.emailAddresses?.[0]?.emailAddress || 'User';

  const userInitials = userName
    .split(' ')
    .map((name) => name.charAt(0))
    .join('')
    .toUpperCase()
    .slice(0, 2);

  return (
    <div className='space-y-6'>
      <div>
        <h2 className='text-lg font-semibold'>Profile</h2>
        <p className='text-sm text-muted-foreground'>
          View your personal information and profile picture.
        </p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Profile Information</CardTitle>
          <CardDescription>
            Your profile information is managed through your account settings.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className='flex items-center gap-3'>
            <Avatar className='h-10 w-10'>
              <AvatarImage src={user?.imageUrl || undefined} alt={userName} />
              <AvatarFallback className='bg-blue-100 text-blue-600 dark:bg-blue-900 dark:text-blue-300 text-xs font-medium'>
                {userInitials}
              </AvatarFallback>
            </Avatar>

            <div className='flex-1'>
              <h3 className='font-medium text-gray-900 dark:text-gray-100 text-sm'>
                {userName}
              </h3>
              <p className='text-xs text-gray-500 dark:text-gray-400'>
                {user?.emailAddresses?.[0]?.emailAddress}
              </p>
              <p className='text-xs text-muted-foreground mt-1'>
                Profile picture is managed through your account provider
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
