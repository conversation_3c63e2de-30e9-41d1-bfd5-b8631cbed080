module.exports = {
  printWidth: 80, // Max line length
  tabWidth: 2, // 2 spaces per tab
  useTabs: false, // Use spaces
  semi: true, // Add semicolons
  singleQuote: true, // Use single quotes
  quoteProps: 'as-needed', // Object keys quoted only if necessary
  jsxSingleQuote: true, // Single quotes in JSX
  trailingComma: 'es5', // Trailing commas in ES5 constructs
  bracketSpacing: true, // Space inside brackets
  bracketSameLine: false, // Put > on a new line in JSX
  arrowParens: 'always', // Always use parentheses in arrow fns
  proseWrap: 'preserve', // Preserve line breaks in Markdown
  htmlWhitespaceSensitivity: 'css', // Respect CSS whitespace rules
  endOfLine: 'lf', // Enforce LF line endings
  embeddedLanguageFormatting: 'auto',
  singleAttributePerLine: false,
};
