interface User {
  id: string;
  email: string;
  name: string;
  role: string;
  status: string;
}

class SearchCache {
  private cache = new Map<string, { users: User[]; timestamp: number }>();
  private maxSize = 50;
  private maxAge = 5 * 60 * 1000; // 5 minutes

  get(key: string): User[] | null {
    const entry = this.cache.get(key);
    if (entry && Date.now() - entry.timestamp < this.maxAge) {
      return entry.users;
    }
    if (entry) {
      this.cache.delete(key);
    }
    return null;
  }

  set(key: string, users: User[]): void {
    if (this.cache.size >= this.maxSize) {
      const oldestKey = this.cache.keys().next().value;
      if (oldestKey) {
        this.cache.delete(oldestKey);
      }
    }
    this.cache.set(key, { users, timestamp: Date.now() });
  }

  getCacheKeys(): string[] {
    return Array.from(this.cache.keys());
  }
}

export const searchCache = new SearchCache();
