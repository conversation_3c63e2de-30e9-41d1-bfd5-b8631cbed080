import { useState, useCallback, useEffect, useMemo } from 'react';
import { useTicketingSelectors } from '@/features/ticketing/store/use-ticketing-store';
import { usePermissions, useAuth } from '@/features/shared/hooks/useAuth';
import type { Ticket } from '@/features/ticketing/models/ticket.schema';
import { CreateTicketFormData } from '@/features/ticketing/models/ticket-form.schema';
import { filterTicketsByRole } from '@/features/ticketing/utils/role-based-filtering';
import { RoleBasedFilterContext } from '@/features/ticketing/types/role-based-filtering';

/**
 * Manages the user-facing workflow of the ticketing page.
 * This includes ticket selection, creation, and draft management.
 *
 * @param tickets - The list of tickets to be managed.
 * @param tenantId - The ID of the current tenant.
 * @param isCacheLoaded - Flag indicating if the cache is loaded.
 * @param hasInitialApiLoad - Flag indicating if the initial API load is complete.
 * @returns An object with state and handlers for the ticketing workflow.
 */
export function useTicketWorkflow(
  tickets: Ticket[],
  tenantId: string | null,
  isCacheLoaded: boolean,
  hasInitialApiLoad: boolean
) {
  const { user } = useAuth();
  const { role } = useAuth();
  const { hasPermission } = usePermissions();

  const selectedTicketId = useTicketingSelectors.useSelectedTicketId();
  const { selectTicket, addTicketOptimistic } =
    useTicketingSelectors.useTicketingActions();

  const [isCreatingTicket, setIsCreatingTicket] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showDraftConfirmation, setShowDraftConfirmation] = useState(false);
  const [pendingTicketId, setPendingTicketId] = useState<string | null>(null);

  const selectedTicket = useMemo(
    () => tickets.find((ticket) => ticket.id === selectedTicketId),
    [tickets, selectedTicketId]
  );

  const myOpenTickets = useMemo(() => {
    if (!user?.id || !tenantId) return [];

    const filterContext: RoleBasedFilterContext = {
      userId: user.id,
      role: role as 'super_admin' | 'admin' | 'agent' | 'user',
      tenantId: tenantId,
      email: user.emailAddresses?.[0]?.emailAddress || '',
    };

    return filterTicketsByRole(tickets, filterContext, { status: ['open'] })
      .tickets;
  }, [tickets, user?.id, role, tenantId, user?.emailAddresses]);

  // Auto-select the first open ticket or open the create form.
  useEffect(() => {
    if (
      !isCacheLoaded ||
      !hasInitialApiLoad ||
      selectedTicketId ||
      isCreatingTicket
    ) {
      return;
    }

    if (myOpenTickets.length > 0 && myOpenTickets[0]) {
      selectTicket(myOpenTickets[0].id);
    } else if (hasPermission('tickets.create')) {
      setIsCreatingTicket(true);
    }
  }, [
    myOpenTickets,
    selectedTicketId,
    isCreatingTicket,
    selectTicket,
    isCacheLoaded,
    hasInitialApiLoad,
    hasPermission,
  ]);

  const handleCreateTicket = useCallback(() => {
    if (hasPermission('tickets.create')) {
      setIsCreatingTicket(true);
      selectTicket(null);
    }
  }, [hasPermission, selectTicket]);

  const handleCancelCreateTicket = useCallback(() => {
    setIsCreatingTicket(false);
  }, []);

  const handleTicketSelect = useCallback(
    (ticketId: string) => {
      if (isCreatingTicket) {
        const draftData = localStorage.getItem('ticket-draft');
        if (draftData) {
          setPendingTicketId(ticketId);
          setShowDraftConfirmation(true);
        } else {
          setIsCreatingTicket(false);
          selectTicket(ticketId);
        }
      } else {
        selectTicket(ticketId);
      }
    },
    [isCreatingTicket, selectTicket]
  );

  const handleSaveChanges = useCallback(() => {
    setShowDraftConfirmation(false);
    setIsCreatingTicket(false);
    if (pendingTicketId) {
      selectTicket(pendingTicketId);
      setPendingTicketId(null);
    }
  }, [pendingTicketId, selectTicket]);

  const handleDiscardChanges = useCallback(() => {
    localStorage.removeItem('ticket-draft');
    setShowDraftConfirmation(false);
    setIsCreatingTicket(false);
    if (pendingTicketId) {
      selectTicket(pendingTicketId);
      setPendingTicketId(null);
    }
  }, [pendingTicketId, selectTicket]);

  const handleDialogClose = useCallback(() => {
    setShowDraftConfirmation(false);
    setPendingTicketId(null);
  }, []);

  const handleSubmitTicket = useCallback(
    async (data: CreateTicketFormData & { attachment_ids: string[] }) => {
      if (!user || !tenantId) return;

      setIsSubmitting(true);
      try {
        const response = await fetch('/api/tickets', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ ...data, tenant_id: tenantId }),
        });

        if (!response.ok) {
          throw new Error('Failed to create ticket');
        }

        const newTicket = (await response.json()).ticket as Ticket;
        addTicketOptimistic(newTicket);
        setIsCreatingTicket(false);
        selectTicket(newTicket.id);
      } catch (error) {
        console.error('Error submitting ticket:', error);
        // Here you might want to show a toast notification
      } finally {
        setIsSubmitting(false);
      }
    },
    [user, tenantId, addTicketOptimistic, selectTicket]
  );

  return {
    selectedTicket,
    isCreatingTicket,
    isSubmitting,
    showDraftConfirmation,
    handleCreateTicket,
    handleCancelCreateTicket,
    handleTicketSelect,
    handleSaveChanges,
    handleDiscardChanges,
    handleDialogClose,
    handleSubmitTicket,
  };
}
