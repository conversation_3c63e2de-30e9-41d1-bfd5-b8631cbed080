'use client';

import { Button } from '@/features/shared/components/ui/button';
import { cn } from '@/lib/utils';
import { Lock, Palette, Shield, User, Users, Zap } from 'lucide-react';
import { useSettingsUI } from '../hooks/useSettingsSync';

interface SettingsNavigationProps {
  activeSection: string;
  hasAdminAccess: boolean;
}

interface NavigationItem {
  id: string;
  label: string;
  icon: React.ComponentType<{ className?: string }>;
  description?: string;
  adminOnly?: boolean;
}

const navigationItems: NavigationItem[] = [
  {
    id: 'profile',
    label: 'Profile',
    icon: User,
    description: 'Personal information and avatar',
  },
  {
    id: 'appearance',
    label: 'Appearance',
    icon: Palette,
    description: 'Theme and display preferences',
  },
  {
    id: 'security',
    label: 'Security',
    icon: Lock,
    description: 'Password and security settings',
  },
  {
    id: 'notifications',
    label: 'Notifications',
    icon: Zap,
    description: 'Email and browser notifications',
  },
  {
    id: 'admin',
    label: 'Administration',
    icon: Shield,
    description: 'Admin-only settings',
    adminOnly: true,
  },
  {
    id: 'assignment',
    label: 'Auto Assignment',
    icon: Users,
    description: 'Default agent and department rules',
    adminOnly: true,
  },
];

export function SettingsNavigation({
  activeSection,
  hasAdminAccess,
}: SettingsNavigationProps) {
  const { setActiveSection } = useSettingsUI();

  const visibleItems = navigationItems.filter(
    (item) => !item.adminOnly || hasAdminAccess
  );

  return (
    <nav className='space-y-1'>
      <div className='mb-4'>
        <h3 className='text-sm font-medium text-muted-foreground uppercase tracking-wider'>
          Settings
        </h3>
      </div>

      {/* User Settings */}
      <div className='space-y-1'>
        {visibleItems
          .filter((item) => !item.adminOnly)
          .map((item) => {
            const Icon = item.icon;
            const isActive = activeSection === item.id;

            return (
              <Button
                key={item.id}
                variant='ghost'
                onClick={() => setActiveSection(item.id)}
                className={cn(
                  'w-full justify-start h-auto p-3 text-left',
                  'hover:bg-muted/50 transition-colors',
                  isActive && 'bg-muted text-foreground font-medium'
                )}
              >
                <div className='flex items-start gap-3 w-full'>
                  <Icon
                    className={cn(
                      'h-4 w-4 mt-1 flex-shrink-0',
                      isActive ? 'text-foreground' : 'text-muted-foreground'
                    )}
                  />
                  <div className='flex-1 min-w-0'>
                    <div
                      className={cn(
                        'text-sm leading-5',
                        isActive
                          ? 'text-foreground font-medium'
                          : 'text-foreground'
                      )}
                    >
                      {item.label}
                    </div>
                    {item.description && (
                      <div className='text-xs text-muted-foreground mt-1 line-clamp-2 leading-4'>
                        {item.description}
                      </div>
                    )}
                  </div>
                </div>
              </Button>
            );
          })}
      </div>

      {/* Admin Settings */}
      {hasAdminAccess && (
        <>
          <div className='pt-4 pb-2'>
            <h3 className='text-sm font-medium text-muted-foreground uppercase tracking-wider'>
              Administration
            </h3>
          </div>

          <div className='space-y-1'>
            {visibleItems
              .filter((item) => item.adminOnly)
              .map((item) => {
                const Icon = item.icon;
                const isActive = activeSection === item.id;

                return (
                  <Button
                    key={item.id}
                    variant='ghost'
                    onClick={() => setActiveSection(item.id)}
                    className={cn(
                      'w-full justify-start h-auto p-3 text-left',
                      'hover:bg-muted/50 transition-colors',
                      isActive && 'bg-muted text-foreground font-medium'
                    )}
                  >
                    <div className='flex items-start gap-3 w-full'>
                      <Icon
                        className={cn(
                          'h-4 w-4 mt-1 flex-shrink-0',
                          isActive ? 'text-foreground' : 'text-muted-foreground'
                        )}
                      />
                      <div className='flex-1 min-w-0'>
                        <div
                          className={cn(
                            'text-sm leading-5',
                            isActive
                              ? 'text-foreground font-medium'
                              : 'text-foreground'
                          )}
                        >
                          {item.label}
                        </div>
                        {item.description && (
                          <div className='text-xs text-muted-foreground mt-1 line-clamp-2 leading-4'>
                            {item.description}
                          </div>
                        )}
                      </div>
                    </div>
                  </Button>
                );
              })}
          </div>
        </>
      )}
    </nav>
  );
}
