import { useCallback } from 'react';

/**
 * Hook for opening tickets (changing status from 'new' to 'open')
 * Enterprise-grade implementation with proper error handling
 */
export function useOpenTicket() {
  const openTicket = useCallback(
    async (ticketId: string): Promise<{ success: boolean; error?: string }> => {
      // Get tenant ID from the current subdomain
      const hostname = window.location.hostname;
      const parts = hostname.split('.');
      const tenantId = parts.length > 1 ? parts[0] : null; // Extract subdomain (e.g., 'quantumnest' from 'quantumnest.localhost')

      if (!tenantId || tenantId === 'localhost' || tenantId === 'www') {
        return { success: false, error: 'Tenant ID not available' };
      }

      try {
        const response = await fetch(`/api/tickets/${ticketId}/open`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            tenant_id: tenantId,
          }),
        });

        if (!response.ok) {
          const errorData = await response.json().catch(() => ({}));
          return {
            success: false,
            error:
              errorData.error ||
              `HTTP ${response.status}: ${response.statusText}`,
          };
        }

        await response.json();
        return { success: true };
      } catch (error) {
        return {
          success: false,
          error:
            error instanceof Error ? error.message : 'Unknown error occurred',
        };
      }
    },
    [] // No dependencies needed since we get tenantId from window.location
  );

  return { openTicket };
}
