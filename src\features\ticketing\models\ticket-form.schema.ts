import { z } from 'zod';

export const CreateTicketFormSchema = z.object({
  title: z.string().min(1, 'Title is required').max(200, 'Title too long'),
  description: z.string().min(1, 'Description is required'),
  priority: z.enum(['low', 'medium', 'high', 'urgent']),
  department: z.enum(['sales', 'support', 'marketing', 'technical']),
  assignedTo: z.string().optional(),
  cc: z.array(z.string()),
});

export type CreateTicketFormData = z.infer<typeof CreateTicketFormSchema>;
