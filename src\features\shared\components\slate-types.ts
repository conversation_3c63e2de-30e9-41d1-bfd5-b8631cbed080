import { BaseEditor, Descendant, Editor, Element } from 'slate';
import { ReactEditor } from 'slate-react';

export { Element };

// Define the custom editor type
export type CustomEditor = BaseEditor & ReactEditor;

// Define element types
export type ParagraphElement = {
  type: 'paragraph';
  children: CustomText[];
};

export type HeadingOneElement = {
  type: 'heading-one';
  children: CustomText[];
};

export type HeadingTwoElement = {
  type: 'heading-two';
  children: CustomText[];
};

export type BulletedListElement = {
  type: 'bulleted-list';
  children: ListItemElement[];
};

export type NumberedListElement = {
  type: 'numbered-list';
  children: ListItemElement[];
};

export type ListItemElement = {
  type: 'list-item';
  children: CustomText[];
};

export type BlockQuoteElement = {
  type: 'block-quote';
  children: CustomText[];
};

export type AlignedElement = {
  type: 'paragraph' | 'heading-one' | 'heading-two';
  align?: 'left' | 'center' | 'right';
  children: CustomText[];
};

export type CustomElement =
  | ParagraphElement
  | HeadingOneElement
  | HeadingTwoElement
  | BulletedListElement
  | NumberedListElement
  | ListItemElement
  | BlockQuoteElement;

// Define text formatting types
export type FormattedText = {
  text: string;
  bold?: boolean;
  italic?: boolean;
  underline?: boolean;
  color?: string; // For text color
};

export type CustomText = FormattedText;

// Extend Slate's types
declare module 'slate' {
  interface CustomTypes {
    Editor: CustomEditor;
    Element: CustomElement;
    Text: CustomText;
  }
}

// Helper type for the editor value
export type SlateValue = Descendant[];

// Default initial value
export const createInitialValue = (): SlateValue => [
  {
    type: 'paragraph',
    children: [{ text: '' }],
  },
];

// Element type guards
export const isBlockActive = (editor: CustomEditor, format: string) => {
  const { selection } = editor;
  if (!selection) return false;

  try {
    // Validate that the selection path exists in the current document
    const { anchor, focus } = selection;

    // Check if the anchor and focus paths are valid
    if (
      !Editor.hasPath(editor, anchor.path) ||
      !Editor.hasPath(editor, focus.path)
    ) {
      return false;
    }

    const [match] = Array.from(
      Editor.nodes(editor, {
        at: selection,
        match: (n) =>
          !Editor.isEditor(n) && Element.isElement(n) && n.type === format,
      })
    );

    return !!match;
  } catch (error) {
    // If any error occurs during path validation or node access, return false
    console.warn('isBlockActive: Invalid selection path detected', error);
    return false;
  }
};

// Mark type guards
export const isMarkActive = (
  editor: CustomEditor,
  format: keyof Omit<FormattedText, 'text'>
) => {
  try {
    // Validate selection if it exists
    const { selection } = editor;
    if (selection) {
      const { anchor, focus } = selection;
      if (
        !Editor.hasPath(editor, anchor.path) ||
        !Editor.hasPath(editor, focus.path)
      ) {
        return false;
      }
    }

    const marks = editor.marks;
    return marks ? marks[format] === true : false;
  } catch (error) {
    console.warn('isMarkActive: Invalid selection path detected', error);
    return false;
  }
};
