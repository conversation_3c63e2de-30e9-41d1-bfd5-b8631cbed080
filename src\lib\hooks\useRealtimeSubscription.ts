/**
 * Modern 2025+ Supabase Real-time Hook
 *
 * A robust, minimal implementation for Supabase Realtime subscriptions
 * with automatic reconnection logic and detailed connection state tracking.
 * It follows the latest Supabase best practices for connection management.
 */
'use client';

import { useEffect, useState, useCallback } from 'react';
import { useRealtime } from '@/features/shared/components/RealtimeProvider';
import type { RealtimePostgresChangesPayload } from '@supabase/supabase-js';

// --- Type Definitions ---

interface RealtimeConfig {
  table: string;
  schema?: string;
  filter?: string;
  event?: 'INSERT' | 'UPDATE' | 'DELETE' | '*';
}

type ConnectionStatus =
  | 'disconnected'
  | 'connecting'
  | 'connected'
  | 'reconnecting'
  | 'closed'
  | 'error';

interface ConnectionState {
  status: ConnectionStatus;
  error: Error | null;
  reconnectAttempts: number;
}

interface RealtimeEvent<T extends DatabaseRecord> {
  id: number;
  type: string;
  timestamp: string;
  payload: RealtimePostgresChangesPayload<T>;
}

interface UseRealtimeSubscriptionReturn<T extends DatabaseRecord> {
  connectionState: ConnectionState;
  data: T[];
  events: RealtimeEvent<T>[];
}

interface DatabaseRecord {
  id: string;
  [key: string]: unknown;
}

// --- Main Hook ---

export function useRealtimeSubscription<
  T extends DatabaseRecord = DatabaseRecord,
>(
  config: RealtimeConfig,
  tenantId: string | null,
  enabled: boolean = true
): UseRealtimeSubscriptionReturn<T> {
  const { connectionManager } = useRealtime();
  const [connectionState, setConnectionState] = useState<ConnectionState>({
    status: 'disconnected',
    error: null,
    reconnectAttempts: 0,
  });
  const [data, setData] = useState<T[]>([]);
  const [events, setEvents] = useState<RealtimeEvent<T>[]>([]);

  const handlePayload = useCallback(
    (payload: RealtimePostgresChangesPayload<T>) => {
      const eventId = Date.now() + Math.random();
      const event: RealtimeEvent<T> = {
        id: eventId,
        type: payload.eventType,
        timestamp: new Date().toISOString(),
        payload,
      };

      setEvents((prev) => [event, ...prev.slice(0, 19)]);

      switch (payload.eventType) {
        case 'INSERT':
          setData((prev) => [payload.new as T, ...prev]);
          break;
        case 'UPDATE':
          setData((prev) =>
            prev.map((item) =>
              item.id === (payload.new as T).id ? (payload.new as T) : item
            )
          );
          break;
        case 'DELETE':
          setData((prev) =>
            prev.filter((item) => item.id !== (payload.old as T).id)
          );
          break;
      }
    },
    []
  );

  useEffect(() => {
    if (!enabled || !tenantId || !connectionManager) {
      return;
    }

    const channelName = `realtime:${config.table}:${tenantId}`;
    connectionManager.connect(channelName);

    const handleStateChange = (state: ConnectionState) => {
      setConnectionState(state);
    };

    connectionManager.subscribe(handleStateChange);

    const channel = connectionManager.getChannel();

    if (channel) {
      const filterOpts = {
        schema: config.schema ?? 'public',
        table: config.table,
        event: config.event ?? '*',
        ...(config.filter ? { filter: config.filter } : {}),
      } as const;

      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      (channel as any).on('postgres_changes', filterOpts, handlePayload);
    }

    return () => {
      connectionManager.unsubscribe(handleStateChange);
    };
  }, [
    enabled,
    tenantId,
    connectionManager,
    config.table,
    config.schema,
    config.filter,
    config.event,
    handlePayload,
  ]);

  return {
    connectionState,
    data,
    events,
  };
}
