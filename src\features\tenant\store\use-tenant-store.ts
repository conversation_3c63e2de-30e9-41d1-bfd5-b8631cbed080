import { create } from 'zustand';
import { Tenant } from '../models/tenant.schema';
import { getDomainFromWindow, DomainInfoState } from '@/lib/domain';

interface TenantState {
  tenant: Tenant | null;
  tenantId: string | null;
  domainInfo: DomainInfoState | null;
  isLoading: boolean;
  error: string | null;
  actions: {
    initializeTenant: () => Promise<void>;
    switchTenant: (tenantId: string) => Promise<void>;
  };
}

export const useTenantStore = create<TenantState>((set) => ({
  tenant: null,
  tenantId: null,
  domainInfo: null,
  isLoading: true,
  error: null,
  actions: {
    initializeTenant: async () => {
      try {
        set({ isLoading: true, error: null });

        const info = getDomainFromWindow(window);
        set({ domainInfo: info });

        if (info.isLocalhost && !info.isSubdomain) {
          set({
            tenantId: null,
            tenant: null,
            isLoading: false,
          });
          return;
        }

        if (info.tenantId) {
          const tenantData = {
            id: info.tenantId,
            name:
              info.tenantId.charAt(0).toUpperCase() + info.tenantId.slice(1),
            subdomain: info.tenantId,
            domain: info.hostname,
            createdAt: new Date(),
            updatedAt: new Date(),
            settings: {
              allowSignup: true,
              requireEmailVerification: true,
              maxUsers: 1000,
              features: ['tickets', 'analytics', 'integrations'],
            },
          };
          set({
            tenantId: info.tenantId,
            tenant: tenantData,
          });
        } else {
          const defaultTenantId = 'localhost';
          const tenantData = {
            id: defaultTenantId,
            name: 'Local Development',
            subdomain: defaultTenantId,
            domain: 'localhost:3000',
            createdAt: new Date(),
            updatedAt: new Date(),
            settings: {
              allowSignup: true,
              requireEmailVerification: false,
              maxUsers: 1000,
              features: ['tickets', 'analytics', 'integrations'],
            },
          };
          set({
            tenantId: defaultTenantId,
            tenant: tenantData,
          });
        }
      } catch (err) {
        set({
          error:
            err instanceof Error ? err.message : 'Failed to initialize tenant',
        });
      } finally {
        set({ isLoading: false });
      }
    },
    switchTenant: async (newTenantId: string) => {
      try {
        set({ isLoading: true, error: null });

        const tenantData = {
          id: newTenantId,
          name: newTenantId.charAt(0).toUpperCase() + newTenantId.slice(1),
          subdomain: newTenantId,
          domain: `${newTenantId}.example.com`,
          createdAt: new Date(),
          updatedAt: new Date(),
          settings: {
            allowSignup: true,
            requireEmailVerification: true,
            maxUsers: 1000,
            features: ['tickets', 'analytics', 'integrations'],
          },
        };
        set({
          tenantId: newTenantId,
          tenant: tenantData,
        });
      } catch (err) {
        set({
          error: err instanceof Error ? err.message : 'Failed to switch tenant',
        });
      } finally {
        set({ isLoading: false });
      }
    },
  },
}));

export const useTenant = () => useTenantStore((state) => state);
export const useTenantActions = () => useTenantStore((state) => state.actions);
