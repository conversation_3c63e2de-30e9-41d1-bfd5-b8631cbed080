'use client';

import { useEffect, useCallback, useMemo } from 'react';
import { useAuth } from '@clerk/nextjs';
import { useTenant } from '@/features/tenant/store/use-tenant-store';
import { useSupabase } from '@/features/shared/components/SupabaseProvider';
import { useTicketingSelectors } from '@/features/ticketing/store/use-ticketing-store';
import { cacheService } from './cache-service';

/**
 * Hook to integrate cache with the application - React 19 Performance Optimized
 * Handles cache initialization, sync, and cleanup with seamless background operations
 *
 * Key Optimizations:
 * - useMemo for expensive computations
 * - useCallback for event handlers
 * - Optimized store selectors for minimal re-renders
 * - Efficient dependency arrays
 */
export function useCacheIntegration() {
  const { isSignedIn } = useAuth();
  const { tenantId } = useTenant();
  const { supabase } = useSupabase();

  // Use optimized selectors for better performance
  const { isCacheLoaded, lastCacheSync, syncWithCache, clearCacheForTenant } =
    useTicketingSelectors.useCacheState();
  const { loadTicketsFromCacheSilent, loadTicketsFromAPI } =
    useTicketingSelectors.useTicketingActions();

  // Memoize cache readiness state
  const isCacheReady = useMemo(
    () => isSignedIn && !!tenantId && !!supabase,
    [isSignedIn, tenantId, supabase]
  );

  // Initialize cache service with Supabase client
  useEffect(() => {
    if (supabase) {
      cacheService.initialize(supabase);
    }
  }, [supabase]);

  // Memoize sync interval calculation
  const syncInterval = useMemo(() => 3 * 60 * 1000, []); // 3 minutes
  const checkInterval = useMemo(() => 30 * 1000, []); // 30 seconds

  // Load cache silently when tenant changes - instant UI rendering
  useEffect(() => {
    if (isCacheReady && !isCacheLoaded) {
      loadTicketsFromCacheSilent(tenantId!);
    }
  }, [isCacheReady, isCacheLoaded, tenantId, loadTicketsFromCacheSilent]);

  // Force fresh data loading on login/reconnect to ensure data consistency
  useEffect(() => {
    if (isCacheReady && isSignedIn && tenantId) {
      console.log(
        '🔄 User logged in, forcing fresh data sync for tenant:',
        tenantId
      );
      // Force a fresh sync to ensure we have the latest data from Supabase
      syncWithCache(tenantId);
      // Also load fresh data from API
      // Note: Role will be determined by the API based on JWT token
      loadTicketsFromAPI(tenantId, 'user', 'all');
    }
  }, [isSignedIn, tenantId, isCacheReady, syncWithCache, loadTicketsFromAPI]);

  // Smart background sync with backend - invisible to user
  useEffect(() => {
    if (!isCacheReady) return;

    const intervalId = setInterval(async () => {
      const timeSinceLastSync = Date.now() - lastCacheSync;

      if (timeSinceLastSync > syncInterval) {
        try {
          await syncWithCache(tenantId!);

          // Perform cache maintenance every 5th sync (roughly every 15 minutes)
          const syncCount = Math.floor(Date.now() / syncInterval);
          if (syncCount % 5 === 0) {
            await cacheService.cleanupOldCache(tenantId!, 24 * 60 * 60 * 1000); // 24 hours
            await cacheService.limitCacheSize(tenantId!, 1000); // Max 1000 entries
          }
        } catch {
          // Silent failure - don't disrupt user experience
          // Error logging handled by individual services
        }
      }
    }, checkInterval);

    return () => clearInterval(intervalId);
  }, [
    isCacheReady,
    tenantId,
    lastCacheSync,
    syncWithCache,
    syncInterval,
    checkInterval,
  ]);

  // Clear cache on logout
  useEffect(() => {
    if (!isSignedIn && tenantId) {
      clearCacheForTenant(tenantId);
    }
  }, [isSignedIn, tenantId, clearCacheForTenant]);

  // Memoize utility functions for performance
  const manualSync = useCallback(async () => {
    if (tenantId) {
      await syncWithCache(tenantId);
    }
  }, [tenantId, syncWithCache]);

  const clearCache = useCallback(async () => {
    if (tenantId) {
      await clearCacheForTenant(tenantId);
    }
  }, [tenantId, clearCacheForTenant]);

  const getCacheStats = useCallback(async () => {
    if (tenantId) {
      return await cacheService.getCacheStats(tenantId);
    }
    return null;
  }, [tenantId]);

  return {
    manualSync,
    clearCache,
    getCacheStats,
    isCacheLoaded,
    lastCacheSync,
  };
}
