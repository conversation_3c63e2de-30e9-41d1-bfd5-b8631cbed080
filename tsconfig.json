{"compilerOptions": {"target": "ES2023", "lib": ["DOM", "DOM.Iterable", "ES2023"], "module": "ESNext", "moduleResolution": "bundler", "baseUrl": "./src", "paths": {"@/*": ["*"], "@/components/*": ["features/shared/components/*"], "@/ui/*": ["features/shared/components/ui/*"]}, "allowJs": false, "strict": true, "noImplicitOverride": true, "noUnusedLocals": true, "noUnusedParameters": true, "noUncheckedIndexedAccess": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "noEmit": true, "incremental": true, "skipLibCheck": true, "esModuleInterop": true, "resolveJsonModule": true, "isolatedModules": true, "useDefineForClassFields": true, "exactOptionalPropertyTypes": true, "jsx": "preserve", "plugins": [{"name": "next"}]}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"], "exclude": ["node_modules"]}