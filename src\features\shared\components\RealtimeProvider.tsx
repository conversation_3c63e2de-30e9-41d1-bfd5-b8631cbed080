'use client';

import { createContext, useContext, useEffect, useState } from 'react';
import { useSupabase } from './SupabaseProvider';
import RealtimeConnectionManager from '@/lib/services/realtime-connection.service';

interface RealtimeContextValue {
  connectionManager: RealtimeConnectionManager | null;
}

const RealtimeContext = createContext<RealtimeContextValue | undefined>(
  undefined
);

export function RealtimeProvider({ children }: { children: React.ReactNode }) {
  const { supabase } = useSupabase();
  const [connectionManager, setConnectionManager] =
    useState<RealtimeConnectionManager | null>(null);

  useEffect(() => {
    if (supabase) {
      const manager = new RealtimeConnectionManager(supabase);
      setConnectionManager(manager);
    }
  }, [supabase]);

  return (
    <RealtimeContext.Provider value={{ connectionManager }}>
      {children}
    </RealtimeContext.Provider>
  );
}

export function useRealtime() {
  const context = useContext(RealtimeContext);
  if (context === undefined) {
    throw new Error('useRealtime must be used within a RealtimeProvider');
  }
  return context;
}
