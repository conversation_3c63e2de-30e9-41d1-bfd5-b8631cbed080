'use client';

import { useState } from 'react';
import { Badge } from '@/features/shared/components/ui/badge';
import { Button } from '@/features/shared/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/features/shared/components/ui/select';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/features/shared/components/ui/dialog';
import { cn } from '@/lib/utils';
import { TicketPriority } from '../models/ticket.schema';
import { usePermissions } from '@/features/shared/hooks/useAuth';
import { priorityConfig } from '../config/ticket-options';

interface ConfirmationDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  title: string;
  description: string;
  onConfirm: () => void;
}

function ConfirmationDialog({
  open,
  onOpenChange,
  title,
  description,
  onConfirm,
}: ConfirmationDialogProps) {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>{title}</DialogTitle>
          <DialogDescription>{description}</DialogDescription>
        </DialogHeader>
        <DialogFooter>
          <Button variant='outline' onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button onClick={onConfirm}>Confirm</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

interface InteractivePriorityBadgeProps {
  currentPriority: TicketPriority;
  onPriorityChange: (newPriority: TicketPriority) => void;
}

export function InteractivePriorityBadge({
  currentPriority,
  onPriorityChange,
}: InteractivePriorityBadgeProps) {
  const { hasPermission } = usePermissions();
  const [confirmDialog, setConfirmDialog] = useState<{
    open: boolean;
    newPriority: TicketPriority | null;
  }>({ open: false, newPriority: null });

  // Use permission system only - priority changes are restricted to admin/super_admin
  const canChangePriority = hasPermission('tickets.priority.change');

  // If user doesn't have permission, render a static badge
  if (!canChangePriority) {
    return (
      <Badge
        className={cn(
          'text-xs transition-all duration-300 ease-in-out',
          priorityConfig[currentPriority].color
        )}
      >
        {currentPriority &&
          currentPriority.charAt(0).toUpperCase() +
            currentPriority.slice(1)}{' '}
        Priority
      </Badge>
    );
  }

  const handlePrioritySelect = (newPriority: TicketPriority) => {
    if (newPriority === currentPriority) return;

    setConfirmDialog({
      open: true,
      newPriority,
    });
  };

  const handleConfirm = () => {
    if (confirmDialog.newPriority) {
      onPriorityChange(confirmDialog.newPriority);
    }
    setConfirmDialog({ open: false, newPriority: null });
  };

  const currentOption = priorityConfig[currentPriority];
  const newOption = confirmDialog.newPriority
    ? priorityConfig[confirmDialog.newPriority]
    : null;

  if (!canChangePriority) {
    return (
      <Badge className={cn('text-xs', priorityConfig[currentPriority].color)}>
        {currentPriority &&
          currentPriority.charAt(0).toUpperCase() +
            currentPriority.slice(1)}{' '}
        Priority
      </Badge>
    );
  }

  return (
    <>
      <Select value={currentPriority} onValueChange={handlePrioritySelect}>
        <SelectTrigger
          className={cn(
            'inline-flex items-center justify-center rounded-md h-6! px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 gap-1 transition-[color,box-shadow] overflow-hidden',
            'border bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100',
            'hover:opacity-80 cursor-pointer'
          )}
        >
          <div
            className={cn(
              'w-1.5 h-1.5 rounded-full mr-1',
              priorityConfig[currentPriority].dotColor
            )}
          />
          <SelectValue>
            {currentPriority &&
              currentPriority.charAt(0).toUpperCase() +
                currentPriority.slice(1)}{' '}
            Priority
          </SelectValue>
        </SelectTrigger>
        <SelectContent>
          {Object.entries(priorityConfig).map(([value, config]) => (
            <SelectItem key={value} value={value}>
              <div className={cn('flex items-center gap-2 text-xs')}>
                <div
                  className={cn(
                    'w-1.5 h-1.5 rounded-full mr-1',
                    config.dotColor
                  )}
                />
                {config.label}
              </div>
            </SelectItem>
          ))}
        </SelectContent>
      </Select>

      <ConfirmationDialog
        open={confirmDialog.open}
        onOpenChange={(open) => setConfirmDialog({ open, newPriority: null })}
        title='Change Priority'
        description={`Change priority from ${currentOption?.label} to ${newOption?.label}?`}
        onConfirm={handleConfirm}
      />
    </>
  );
}
