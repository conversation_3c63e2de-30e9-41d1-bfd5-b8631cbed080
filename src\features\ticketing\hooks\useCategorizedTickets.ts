import { useMemo } from 'react';
import { useUser } from '@clerk/nextjs';
import { useAuth } from '@/features/shared/hooks/useAuth';
import { useTenantStore } from '@/features/tenant/store/use-tenant-store';
import { Ticket } from '../models/ticket.schema';
import {
  filterTicketsByRole,
  getRoleDashboardConfig,
} from '../utils/role-based-filtering';
import { RoleBasedFilterContext } from '../types/role-based-filtering';

/**
 * Hook to categorize tickets based on the user's role.
 *
 * @param tickets - The list of tickets to categorize.
 * @returns An object with categorized tickets (new, open, closed).
 */
export function useCategorizedTickets(tickets: Ticket[]) {
  const { user } = useUser();
  const { role } = useAuth();
  const tenantId = useTenantStore((state) => state.tenantId);

  const userEmail = useMemo(
    () => user?.emailAddresses?.[0]?.emailAddress,
    [user?.emailAddresses]
  );

  const filterContext: RoleBasedFilterContext = useMemo(
    () => ({
      userId: user?.id || '',
      role: role as 'super_admin' | 'admin' | 'agent' | 'user',
      tenantId: tenantId || '',
      email: userEmail || '',
    }),
    [user?.id, role, tenantId, userEmail]
  );

  const categorizedTickets = useMemo(() => {
    // Get the role-based dashboard configuration
    const dashboardConfig = getRoleDashboardConfig(role || 'user');

    let newTickets: Ticket[] = [];
    let openTickets: Ticket[] = [];

    // Apply the correct filter functions based on role
    for (const section of dashboardConfig.sections) {
      if (section.key === 'new') {
        newTickets = section.filterFn(tickets, filterContext);
      } else if (section.key === 'assigned' || section.key === 'my') {
        // For super_admin/admin: "assigned" section shows assigned tickets
        // For user: "my" section shows their tickets
        openTickets = section.filterFn(tickets, filterContext);
      }
    }

    // For users, we still need to handle the case where they don't have an "assigned" section
    if (role === 'user') {
      // Users see all their tickets in the "open" section
      openTickets = filterTicketsByRole(tickets, filterContext, {
        status: ['new', 'open', 'pending', 'resolved'],
      }).tickets;
    }

    const closedTickets = filterTicketsByRole(tickets, filterContext, {
      status: ['closed', 'resolved'],
    }).tickets;

    return { newTickets, openTickets, closedTickets };
  }, [tickets, filterContext, role]);

  return categorizedTickets;
}
