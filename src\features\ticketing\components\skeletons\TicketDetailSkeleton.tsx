'use client';

import { cn } from '@/lib/utils';
import {
  Skeleton,
  SkeletonText,
  SkeletonAvatar,
  SkeletonBadge,
  SkeletonButton,
} from '@/features/shared/components/ui/skeleton';

interface TicketDetailSkeletonProps {
  className?: string;
}

/**
 * Message item skeleton that matches MessageItem component structure
 */
function MessageItemSkeleton({
  isExpanded = true,
  hasAttachments = false,
}: {
  isExpanded?: boolean;
  hasAttachments?: boolean;
}) {
  return (
    <div className='flex gap-3 py-4 px-6 transition-colors'>
      {/* Avatar */}
      <SkeletonAvatar className='mt-1' />

      {/* Content */}
      <div className='flex-1 min-w-0'>
        {/* Header with name and timestamp */}
        <div className='flex items-start justify-between mb-1'>
          <div className='flex-1'>
            <div className='flex items-center gap-2'>
              <Skeleton className='h-4 w-24' />
              <Skeleton className='h-3 w-20' />
            </div>
          </div>
          <div className='flex items-center gap-2 ml-4'>
            <Skeleton className='h-4 w-4 rounded' />
          </div>
        </div>

        {/* Content */}
        <div className={cn('text-sm', isExpanded ? 'mt-2' : '')}>
          {isExpanded ? (
            <SkeletonText lines={4} />
          ) : (
            <Skeleton className='h-4 w-full' />
          )}
        </div>

        {/* Attachments section */}
        {isExpanded && hasAttachments && (
          <div className='mt-4'>
            <Skeleton className='h-4 w-20 mb-3' />
            <div className='flex flex-wrap gap-3'>
              <Skeleton className='h-16 w-32 rounded-md' />
              <Skeleton className='h-16 w-32 rounded-md' />
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

/**
 * Unified ticket detail skeleton that matches actual content structure
 */
function UnifiedTicketDetailSkeleton({ className }: { className?: string }) {
  return (
    <div
      className={cn(
        'flex-1 h-[calc(100%-3rem)] my-6 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm flex flex-col overflow-hidden',
        className
      )}
    >
      {/* Fixed Header */}
      <div className='p-6 border-b border-gray-200 dark:border-gray-700 shrink-0'>
        <div className='flex items-center justify-between mb-4'>
          <Skeleton className='h-6 w-64' />
          <SkeletonButton size='sm' className='w-40' />
        </div>

        {/* Metadata section */}
        <div className='flex items-center gap-4 text-sm text-gray-600 dark:text-gray-400'>
          <div className='flex items-center gap-2'>
            <SkeletonAvatar size='sm' />
            <div>
              <Skeleton className='h-3 w-20 mb-1' />
              <Skeleton className='h-3 w-32' />
            </div>
          </div>
          <div className='flex gap-2'>
            <SkeletonBadge />
            <SkeletonBadge />
            <SkeletonBadge />
          </div>
        </div>
      </div>

      {/* Scrollable Content Area */}
      <div className='flex-1 overflow-y-auto min-h-0'>
        <div className='space-y-0'>
          {/* First reply message - collapsed */}
          <MessageItemSkeleton isExpanded={false} hasAttachments={false} />

          {/* Separator */}
          <div className='border-t border-gray-200 dark:border-gray-700' />

          {/* Second reply message - collapsed */}
          <MessageItemSkeleton isExpanded={false} hasAttachments={false} />

          {/* Separator */}
          <div className='border-t border-gray-200 dark:border-gray-700' />

          {/* Original ticket message - expanded (at bottom) */}
          <MessageItemSkeleton isExpanded={true} hasAttachments={false} />
        </div>

        {/* Reply Section - moved inside scrollable area to eliminate gap */}
        <div className='border-t border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800'>
          <div className='p-6'>
            <div className='space-y-4'>
              {/* Reply to header */}
              <div className='flex items-center gap-3 text-sm text-gray-600 dark:text-gray-400'>
                <SkeletonAvatar className='h-8 w-8' />
                <div className='flex items-center gap-2'>
                  <Skeleton className='h-4 w-16' />
                  <Skeleton className='h-4 w-48' />
                  <Skeleton className='h-6 w-6 rounded' />
                </div>
              </div>

              {/* Rich text editor skeleton */}
              <Skeleton className='h-[180px] w-full rounded-md' />

              {/* File upload and submit button */}
              <div className='flex items-center justify-between'>
                <Skeleton className='h-8 w-32' />
                <SkeletonButton className='w-24' />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

/**
 * Main TicketDetailSkeleton component with unified layout
 */
export function TicketDetailSkeleton({ className }: TicketDetailSkeletonProps) {
  return <UnifiedTicketDetailSkeleton {...(className && { className })} />;
}

export { MessageItemSkeleton, UnifiedTicketDetailSkeleton };
