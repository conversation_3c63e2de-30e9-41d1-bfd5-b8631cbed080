'use client';

import { Button } from '@/features/shared/components/ui/button';
import { Download, FileText, Image as ImageIcon } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Attachment } from '../models/ticket.schema';

export function AttachmentItem({ attachment }: { attachment: Attachment }) {
  const isImage = attachment.type.startsWith('image/');
  const isPdf = attachment.type === 'application/pdf';

  const formatFileSize = (bytes: number) => {
    if (bytes < 1024) return `${bytes} B`;
    if (bytes < 1024 * 1024) return `${Math.round(bytes / 1024)} KB`;
    return `${Math.round(bytes / (1024 * 1024))} MB`;
  };

  return (
    <div className='inline-flex items-center gap-3 p-3 border rounded-lg bg-gray-50 dark:bg-gray-700/40 border-gray-200 dark:border-gray-600 min-w-0 max-w-xs'>
      <div
        className={cn(
          'flex items-center justify-center w-10 h-10 rounded',
          isPdf
            ? 'bg-red-100 dark:bg-red-900/20'
            : isImage
              ? 'bg-blue-100 dark:bg-blue-900/20'
              : 'bg-gray-100 dark:bg-gray-600'
        )}
      >
        {isPdf ? (
          <FileText className='h-5 w-5 text-red-600 dark:text-red-400' />
        ) : isImage ? (
          <ImageIcon className='h-5 w-5 text-blue-600 dark:text-blue-400' />
        ) : (
          <FileText className='h-5 w-5 text-gray-600 dark:text-gray-400' />
        )}
      </div>
      <div className='flex-1 min-w-0'>
        <p className='text-sm font-medium text-gray-900 dark:text-gray-100 truncate'>
          {attachment.name}
        </p>
        <p className='text-xs text-gray-500 dark:text-gray-400'>
          {formatFileSize(attachment.size)}
        </p>
      </div>
      <Button
        variant='ghost'
        size='sm'
        className='shrink-0 hover:bg-gray-200 dark:hover:bg-gray-600'
      >
        <Download className='h-4 w-4' />
      </Button>
    </div>
  );
}
