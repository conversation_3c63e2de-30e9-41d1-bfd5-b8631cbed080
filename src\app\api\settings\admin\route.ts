import { createServiceSupabaseClient } from '@/lib/supabase-server';
import { auth } from '@clerk/nextjs/server';
import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';

// Admin Settings Schema
const AdminSettingsUpdateSchema = z.object({
  default_agent_id: z.string().uuid().optional().nullable(),
  default_agent_is_active: z.boolean().optional(),
  auto_assignment_rules: z
    .array(
      z.object({
        id: z.string().uuid().optional(),
        department: z.enum(['sales', 'support', 'marketing', 'technical']),
        assigned_agent_id: z.string().uuid().optional().nullable(),
        is_default: z.boolean().optional(),
        priority: z.number().int().min(1).max(100).optional(),
        is_active: z.boolean().optional(),
      })
    )
    .optional(),
});

// Helper function to check admin permissions
async function checkAdminPermissions(userId: string) {
  const serviceClient = createServiceSupabaseClient();

  const { data: currentUser, error: userError } = await serviceClient
    .from('users')
    .select('id, tenant_id, role')
    .eq('clerk_id', userId)
    .single();

  if (userError || !currentUser) {
    throw new Error('User not found in database');
  }

  if (currentUser.role !== 'admin' && currentUser.role !== 'super_admin') {
    throw new Error('Insufficient permissions');
  }

  return currentUser;
}

// PUT - Update admin settings (admin/super_admin only)
export async function PUT(_request: NextRequest) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check admin permissions
    let currentUser;
    try {
      currentUser = await checkAdminPermissions(userId);
    } catch (error) {
      return NextResponse.json(
        { error: error instanceof Error ? error.message : 'Permission denied' },
        { status: 403 }
      );
    }

    const body = await _request.json();
    const validation = AdminSettingsUpdateSchema.safeParse(body);

    if (!validation.success) {
      return NextResponse.json(
        {
          error: 'Invalid admin settings data',
          details: validation.error.errors,
        },
        { status: 400 }
      );
    }

    const { default_agent_id, default_agent_is_active, auto_assignment_rules } =
      validation.data;
    const serviceClient = createServiceSupabaseClient();

    // Update default agent settings if provided
    if (
      default_agent_id !== undefined ||
      default_agent_is_active !== undefined
    ) {
      if (default_agent_id) {
        // Use upsert to handle the unique constraint on tenant_id
        // This will update if exists, insert if not
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const upsertResult = await (serviceClient as any)
          .from('default_agent_settings')
          .upsert(
            {
              tenant_id: currentUser.tenant_id,
              default_agent_id,
              is_active: default_agent_is_active ?? true,
              created_by: currentUser.id,
              updated_at: new Date().toISOString(),
            },
            {
              onConflict: 'tenant_id', // Use tenant_id as the conflict resolution column
            }
          );

        if (upsertResult.error) {
          throw new Error(
            `Failed to set default agent: ${upsertResult.error.message}`
          );
        }
      } else if (default_agent_id === null) {
        // If default_agent_id is null, deactivate the existing setting
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        await (serviceClient as any)
          .from('default_agent_settings')
          .update({ is_active: false, default_agent_id: null })
          .eq('tenant_id', currentUser.tenant_id);
      } else if (default_agent_is_active !== undefined) {
        // Only updating the active status
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        await (serviceClient as any)
          .from('default_agent_settings')
          .update({ is_active: default_agent_is_active })
          .eq('tenant_id', currentUser.tenant_id);
      }
    }

    // Update auto assignment rules if provided
    if (auto_assignment_rules) {
      // First, deactivate all existing rules for this tenant
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      await (serviceClient as any)
        .from('auto_assignment_rules')
        .update({ is_active: false })
        .eq('tenant_id', currentUser.tenant_id);

      // Insert or update new rules
      for (const rule of auto_assignment_rules) {
        if (rule.assigned_agent_id) {
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          await (serviceClient as any).from('auto_assignment_rules').upsert({
            id: rule.id,
            tenant_id: currentUser.tenant_id,
            department: rule.department,
            assigned_agent_id: rule.assigned_agent_id,
            is_default: rule.is_default || false,
            priority: rule.priority || 1,
            is_active: rule.is_active !== false,
            created_by: currentUser.id,
            updated_at: new Date().toISOString(),
          });
        }
      }
    }

    // Fetch updated settings to return
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const { data: defaultAgentSettings } = await (serviceClient as any)
      .from('default_agent_settings')
      .select('*')
      .eq('tenant_id', currentUser.tenant_id)
      .eq('is_active', true)
      .single();

    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const { data: updatedRules } = await (serviceClient as any)
      .from('auto_assignment_rules')
      .select('*')
      .eq('tenant_id', currentUser.tenant_id)
      .eq('is_active', true)
      .order('priority', { ascending: true });

    return NextResponse.json({
      success: true,
      data: {
        default_agent_settings: defaultAgentSettings,
        auto_assignment_rules: updatedRules || [],
      },
    });
  } catch (error) {
    return NextResponse.json(
      { error: 'Failed to update admin settings' },
      { status: 500 }
    );
  }
}

// DELETE - Reset admin settings (admin/super_admin only)
// eslint-disable-next-line @typescript-eslint/no-unused-vars
export async function DELETE(_request: NextRequest) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check admin permissions
    let currentUser;
    try {
      currentUser = await checkAdminPermissions(userId);
    } catch (error) {
      return NextResponse.json(
        { error: error instanceof Error ? error.message : 'Permission denied' },
        { status: 403 }
      );
    }

    const serviceClient = createServiceSupabaseClient();

    // Deactivate all default agent settings
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    await (serviceClient as any)
      .from('default_agent_settings')
      .update({ is_active: false })
      .eq('tenant_id', currentUser.tenant_id);

    // Deactivate all auto assignment rules
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    await (serviceClient as any)
      .from('auto_assignment_rules')
      .update({ is_active: false })
      .eq('tenant_id', currentUser.tenant_id);

    return NextResponse.json({
      success: true,
      message: 'Admin settings reset successfully',
    });
  } catch (error) {
    console.error('Admin settings reset error:', error);
    return NextResponse.json(
      { error: 'Failed to reset admin settings' },
      { status: 500 }
    );
  }
}
