'use client';

import { useRef } from 'react';
import { CreateTicketFormData } from '../models/ticket-form.schema';
import { Button } from '@/features/shared/components/ui/button';
import { Input } from '@/features/shared/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/features/shared/components/ui/select';
import {
  Form,
  FormField,
  FormItem,
  FormLabel,
} from '@/features/shared/components/ui/form';
import { UserAutocomplete } from '@/features/shared/components';
import { DynamicRichTextEditor } from '@/features/shared/components/DynamicRichTextEditor';
import { DynamicFileUpload } from '@/features/shared/components/DynamicFileUpload';
import { cn } from '@/lib/utils';
import { Send, Trash2 } from 'lucide-react';
import { useAuth } from '@/features/shared/hooks/useAuth';
import { useCreateTicket } from '../hooks/useCreateTicket';
import { priorityConfig, departmentConfig } from '../config/ticket-options';

interface CreateTicketFormProps {
  onSubmit: (
    data: CreateTicketFormData & { attachment_ids: string[] }
  ) => Promise<void>;
  onDiscard?: () => void;
  isSubmitting?: boolean;
  tenantId?: string | undefined;
}

const DropdownOption = ({
  config,
}: {
  config: { color: string; dotColor: string; label: string };
}) => (
  <div className='flex items-center gap-2 text-xs'>
    <div className={cn('w-1.5 h-1.5 mr-1 rounded-full', config.dotColor)} />
    {config.label}
  </div>
);

export function CreateTicketForm({
  onSubmit,
  onDiscard,
  isSubmitting = false,
  tenantId,
}: CreateTicketFormProps) {
  const { role } = useAuth();
  const fileInputRef = useRef<HTMLInputElement>(null);
  const {
    form,
    uploadedFiles,
    setUploadedFiles,
    handleSubmit,
    handleDiscard: discard,
  } = useCreateTicket(tenantId ?? null, onSubmit);

  const canUseAdvancedFields = role === 'super_admin' || role === 'admin';

  const handleAttachClick = () => {
    fileInputRef.current?.click();
  };

  const handleDiscard = () => {
    discard();
    onDiscard?.();
  };

  return (
    <div className='flex-1 h-[calc(100%-3rem)] my-6 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm flex flex-col overflow-hidden'>
      <div className='flex-1 overflow-auto'>
        {/* Form Content - Scrollable */}
        <div className='flex-1 overflow-y-auto min-h-0'>
          <div className='p-6'>
            <Form {...form}>
              <form
                onSubmit={form.handleSubmit(handleSubmit)}
                className='space-y-4'
              >
                {/* Header */}
                <div className='border-b border-gray-200 dark:border-gray-700 pb-4 mb-4'>
                  <div className='flex items-center justify-between'>
                    <h1 className='text-xl font-semibold text-gray-900 dark:text-gray-100'>
                      Create New Ticket
                    </h1>
                    <div className='flex items-center gap-2'>
                      {/* Priority Dropdown */}
                      <FormField
                        control={form.control}
                        name='priority'
                        render={({ field }) => (
                          <Select
                            value={field.value}
                            onValueChange={field.onChange}
                          >
                            <SelectTrigger
                              className={cn(
                                'inline-flex items-center justify-center rounded-md h-6 px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 gap-1 transition-[color,box-shadow] overflow-hidden',
                                'border bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100',
                                'hover:opacity-80 cursor-pointer'
                              )}
                            >
                              <div
                                className={cn(
                                  'w-1.5 h-1.5 rounded-full mr-1',
                                  priorityConfig[field.value].dotColor
                                )}
                              />
                              <SelectValue>
                                {field.value.charAt(0).toUpperCase() +
                                  field.value.slice(1)}{' '}
                                Priority
                              </SelectValue>
                            </SelectTrigger>
                            <SelectContent>
                              {Object.entries(priorityConfig).map(
                                ([value, config]) => (
                                  <SelectItem key={value} value={value}>
                                    <DropdownOption config={config} />
                                  </SelectItem>
                                )
                              )}
                            </SelectContent>
                          </Select>
                        )}
                      />

                      {/* Department Dropdown */}
                      <FormField
                        control={form.control}
                        name='department'
                        render={({ field }) => (
                          <Select
                            value={field.value}
                            onValueChange={field.onChange}
                          >
                            <SelectTrigger
                              className={cn(
                                'inline-flex items-center justify-center rounded-md h-6 px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 gap-1 transition-[color,box-shadow] overflow-hidden',
                                'border bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100',
                                'hover:opacity-80 cursor-pointer'
                              )}
                            >
                              <div
                                className={cn(
                                  'w-1.5 h-1.5 mr-1 rounded-full',
                                  departmentConfig[field.value].dotColor
                                )}
                              />
                              <SelectValue>
                                {field.value.charAt(0).toUpperCase() +
                                  field.value.slice(1)}{' '}
                                Department
                              </SelectValue>
                            </SelectTrigger>
                            <SelectContent>
                              {Object.entries(departmentConfig).map(
                                ([value, config]) => (
                                  <SelectItem key={value} value={value}>
                                    <DropdownOption config={config} />
                                  </SelectItem>
                                )
                              )}
                            </SelectContent>
                          </Select>
                        )}
                      />
                    </div>
                  </div>
                </div>
                {/* Assign To Field - Only for Admins and Super Admins */}
                {canUseAdvancedFields && (
                  <FormField
                    control={form.control}
                    name='assignedTo'
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className='text-sm font-medium text-gray-700 dark:text-gray-300'>
                          Assign To (Admins & Agents)
                        </FormLabel>
                        <UserAutocomplete
                          value={field.value || ''}
                          onChange={field.onChange}
                          placeholder='Select user to assign...'
                          roleFilter={['admin', 'agent']}
                          multiple={false}
                          dropdownOnly={true}
                          returnUserIds={true}
                          className='h-10 no-focus-ring'
                        />
                        {!field.value && (
                          <p className='text-xs text-muted-foreground mt-1'>
                            💡 Leave empty to apply auto-assignment rules based
                            on department
                          </p>
                        )}
                      </FormItem>
                    )}
                  />
                )}

                {/* CC Field - Only for Admins and Super Admins */}
                {canUseAdvancedFields && (
                  <FormField
                    control={form.control}
                    name='cc'
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className='text-sm font-medium text-gray-700 dark:text-gray-300'>
                          CC
                        </FormLabel>
                        <UserAutocomplete
                          value={field.value || []}
                          onChange={field.onChange}
                          placeholder='Type email to search users or enter email...'
                          roleFilter={['admin', 'agent']}
                          multiple={true}
                          className='h-10 no-focus-ring'
                        />
                      </FormItem>
                    )}
                  />
                )}

                {/* Title Field */}
                <FormField
                  control={form.control}
                  name='title'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className='text-sm font-medium text-gray-700 dark:text-gray-300'>
                        Title *
                      </FormLabel>
                      <Input
                        placeholder='Brief description of the issue'
                        className='h-10'
                        {...field}
                      />
                    </FormItem>
                  )}
                />

                {/* Description Field */}
                <FormField
                  control={form.control}
                  name='description'
                  render={({ field }) => (
                    <FormItem>
                      <DynamicRichTextEditor
                        value={field.value}
                        onChange={field.onChange}
                        placeholder='Describe the issue in detail...'
                        disabled={isSubmitting}
                        onAttachClick={handleAttachClick}
                      />
                    </FormItem>
                  )}
                />

                {/* File Upload */}
                <DynamicFileUpload
                  files={uploadedFiles}
                  onFilesChange={setUploadedFiles}
                  disabled={isSubmitting}
                  fileInputRef={fileInputRef}
                />

                {/* Submit buttons */}
                <div className='flex items-center justify-end gap-2'>
                  <Button
                    type='button'
                    variant='outline'
                    onClick={handleDiscard}
                    disabled={isSubmitting}
                  >
                    <Trash2 className='h-4 w-4 mr-1' />
                    Discard
                  </Button>
                  <Button type='submit' disabled={isSubmitting}>
                    <Send className='h-4 w-4 mr-1' />
                    {isSubmitting ? 'Creating...' : 'Create Ticket'}
                  </Button>
                </div>
              </form>
            </Form>
          </div>
        </div>
      </div>
    </div>
  );
}
