'use client';

import { SupabaseClient } from '@supabase/supabase-js';
import { RealtimeChannel } from '@supabase/realtime-js';

type ConnectionStatus =
  | 'disconnected'
  | 'connecting'
  | 'connected'
  | 'reconnecting'
  | 'closed'
  | 'error';

interface ConnectionState {
  status: ConnectionStatus;
  error: Error | null;
  reconnectAttempts: number;
}

type Listener = (state: ConnectionState) => void;

class RealtimeConnectionManager {
  private supabase: SupabaseClient;
  private channel: RealtimeChannel | null = null;
  private listeners: Set<Listener> = new Set();
  private connectionState: ConnectionState = {
    status: 'disconnected',
    error: null,
    reconnectAttempts: 0,
  };
  private reconnectTimer: NodeJS.Timeout | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private initialReconnectDelay = 1000;

  constructor(supabase: SupabaseClient) {
    this.supabase = supabase;
  }

  public connect(channelName: string) {
    if (this.channel) {
      this.disconnect();
    }

    this.updateState({
      status: 'connecting',
      error: null,
      reconnectAttempts: 0,
    });
    this.channel = this.supabase.channel(channelName, {
      config: {
        broadcast: {
          ack: true,
        },
      },
    });

    this.channel
      .on('broadcast', { event: 'test' }, (payload) => console.log(payload))
      .subscribe((status, err) => {
        switch (status) {
          case 'SUBSCRIBED':
            this.updateState({
              status: 'connected',
              error: null,
              reconnectAttempts: 0,
            });
            this.reconnectAttempts = 0;
            if (this.reconnectTimer) {
              clearTimeout(this.reconnectTimer);
            }
            break;
          case 'TIMED_OUT':
          case 'CHANNEL_ERROR':
          case 'CLOSED':
            this.updateState({
              status: status === 'CLOSED' ? 'closed' : 'error',
              error: err || new Error(`Connection status: ${status}`),
            });
            this.reconnect();
            break;
        }
      });
  }

  public disconnect() {
    if (this.channel) {
      this.supabase.removeChannel(this.channel);
      this.channel = null;
      this.updateState({ status: 'disconnected', error: null });
    }
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
    }
  }

  public subscribe(listener: Listener) {
    this.listeners.add(listener);
    listener(this.connectionState);
  }

  public unsubscribe(listener: Listener) {
    this.listeners.delete(listener);
  }

  private updateState(newState: Partial<ConnectionState>) {
    this.connectionState = {
      ...this.connectionState,
      ...newState,
    } as ConnectionState;
    this.listeners.forEach((listener) => listener(this.connectionState));
  }

  private reconnect() {
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
    }
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      this.updateState({
        status: 'error',
        error: new Error('Maximum reconnection attempts reached.'),
      });
      return;
    }

    this.reconnectAttempts++;
    const delay =
      this.initialReconnectDelay * 2 ** (this.reconnectAttempts - 1);

    this.updateState({ status: 'reconnecting' });

    this.reconnectTimer = setTimeout(() => {
      if (this.channel) {
        this.connect(this.channel.topic);
      }
    }, delay);
  }

  public getChannel() {
    return this.channel;
  }
}

export default RealtimeConnectionManager;
