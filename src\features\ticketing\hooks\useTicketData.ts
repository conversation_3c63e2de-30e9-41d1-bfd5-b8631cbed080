import { useEffect, useMemo, useState } from 'react';
import { useTicketingSelectors } from '@/features/ticketing/store/use-ticketing-store';
import { useAuth } from '@/features/shared/hooks/useAuth';
import type { Ticket } from '@/features/ticketing/models/ticket.schema';

/**
 * Hook for fetching and managing ticket data for a specific tenant.
 * It handles loading states and provides a sorted list of tickets.
 *
 * @param tenantId - The ID of the tenant whose tickets are to be fetched.
 * @param initialTickets - Optional array of tickets to initialize the store with.
 * @returns An object containing the tickets, loading state, and initial load status.
 */
export function useTicketData(
  tenantId: string | null,
  initialTickets: Ticket[] = []
) {
  const { user, role } = useAuth();
  const userId = user?.id;

  const allTickets = useTicketingSelectors.useTickets();
  const isLoadingFromStore = useTicketingSelectors.useIsLoading();
  const isCacheLoaded = useTicketingSelectors.useIsCacheLoaded();
  const { loadTicketsFromAPI, setTickets } =
    useTicketingSelectors.useTicketingActions();

  const [hasInitialApiLoad, setHasInitialApiLoad] = useState(
    initialTickets.length > 0
  );

  // Effect to trigger the initial API load when the tenant and user are ready.
  useEffect(() => {
    if (initialTickets.length > 0) {
      setTickets(initialTickets);
    } else if (tenantId && userId && role && !hasInitialApiLoad) {
      loadTicketsFromAPI(tenantId, role, 'all').finally(() => {
        setHasInitialApiLoad(true);
      });
    }
  }, [
    tenantId,
    userId,
    role,
    loadTicketsFromAPI,
    hasInitialApiLoad,
    initialTickets,
    setTickets,
  ]);

  // Memoized list of tickets, filtered by the current tenant and sorted by creation date.
  const tickets = useMemo(() => {
    const filteredTickets = tenantId
      ? allTickets.filter((ticket: Ticket) => ticket.tenantId === tenantId)
      : [];

    return filteredTickets.sort(
      (a, b) =>
        new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
    );
  }, [allTickets, tenantId]);

  const isLoading =
    isLoadingFromStore || (!isCacheLoaded && !hasInitialApiLoad && !!tenantId);

  return {
    tickets,
    isLoading,
    isCacheLoaded,
    hasInitialApiLoad,
  };
}
