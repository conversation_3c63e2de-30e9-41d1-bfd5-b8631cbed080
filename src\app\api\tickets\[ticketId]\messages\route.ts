import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { z } from 'zod';

// POST method for creating ticket messages (replies)
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ ticketId: string }> }
) {
  try {
    // Await params before using
    const { ticketId } = await params;

    // Authentication check
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Note: Using service client for database operations, no additional token needed

    // Parse request body
    const body = await request.json();

    // Validate input
    const messageSchema = z.object({
      content: z.string().min(1, 'Message content is required'),
      tenant_id: z.string().uuid('Valid tenant ID required'),
      message_type: z
        .enum(['message', 'note', 'status_change'])
        .default('message'),
      is_internal: z.boolean().default(false),
      attachment_ids: z.array(z.string().uuid()).optional().default([]),
    });

    const validatedData = messageSchema.parse(body);

    // Create service client for database operations
    const { createServiceSupabaseClient } = await import(
      '@/lib/supabase-server'
    );
    const serviceSupabase = createServiceSupabaseClient();

    // Verify ticket exists and user has access
    const { data: ticket, error: ticketError } = await serviceSupabase
      .from('tickets')
      .select('id, tenant_id')
      .eq('id', ticketId)
      .eq('tenant_id', validatedData.tenant_id)
      .single();

    if (ticketError || !ticket) {
      return NextResponse.json(
        { error: 'Ticket not found or access denied' },
        { status: 404 }
      );
    }

    // Get user information
    const { data: user, error: userError } = await serviceSupabase
      .from('users')
      .select('id, tenant_id')
      .eq('clerk_id', userId)
      .eq('tenant_id', validatedData.tenant_id)
      .single();

    if (userError || !user) {
      return NextResponse.json(
        { error: 'User not found or access denied' },
        { status: 403 }
      );
    }

    // Create the ticket message
    const { data: message, error: messageError } = await serviceSupabase
      .from('ticket_messages')
      .insert({
        tenant_id: validatedData.tenant_id,
        ticket_id: ticketId,
        author_id: user.id,
        content: validatedData.content,
        message_type: validatedData.message_type,
        is_internal: validatedData.is_internal,
      })
      .select('*')
      .single();

    if (messageError) {
      console.error('Error creating ticket message:', messageError);
      return NextResponse.json(
        { error: 'Failed to create message' },
        { status: 500 }
      );
    }

    // Link attachments to the message if any were provided
    if (
      validatedData.attachment_ids &&
      validatedData.attachment_ids.length > 0
    ) {
      const { data: updatedAttachments, error: attachmentError } =
        await serviceSupabase
          .from('attachments')
          .update({ message_id: message.id, ticket_id: null })
          .in('id', validatedData.attachment_ids)
          .eq('tenant_id', validatedData.tenant_id)
          .eq('uploaded_by', user.id)
          .select('id');

      if (attachmentError) {
        console.error('Failed to link attachments:', attachmentError);
        // Return a warning but don't fail the message creation
        return NextResponse.json(
          {
            message:
              'Reply created successfully but some attachments failed to link',
            data: message,
            warning:
              'Some attachments may not be properly linked to this message',
          },
          { status: 201 }
        );
      }

      // Verify all attachments were linked
      const linkedCount = updatedAttachments?.length || 0;
      if (linkedCount !== validatedData.attachment_ids.length) {
        console.warn(
          `Only ${linkedCount} of ${validatedData.attachment_ids.length} attachments were linked`
        );
        return NextResponse.json(
          {
            message:
              'Reply created successfully but not all attachments were linked',
            data: message,
            warning: `Only ${linkedCount} of ${validatedData.attachment_ids.length} attachments were linked`,
          },
          { status: 201 }
        );
      }
    }

    // Update ticket's updated_at timestamp
    await serviceSupabase
      .from('tickets')
      .update({ updated_at: new Date().toISOString() })
      .eq('id', ticketId);

    return NextResponse.json(
      {
        message: 'Reply created successfully',
        data: message,
      },
      { status: 201 }
    );
  } catch (error) {
    console.error('Error in ticket messages API:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          error: 'Validation failed',
          details: error.errors,
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// GET method for fetching ticket messages
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ ticketId: string }> }
) {
  try {
    // Await params before using
    const { ticketId } = await params;

    // Authentication check
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Get tenant_id from query parameters
    const { searchParams } = new URL(request.url);
    const tenantParam = searchParams.get('tenant_id');

    if (!tenantParam) {
      return NextResponse.json(
        { error: 'tenant_id parameter is required' },
        { status: 400 }
      );
    }

    // Create service client
    const { createServiceSupabaseClient } = await import(
      '@/lib/supabase-server'
    );
    const serviceSupabase = createServiceSupabaseClient();

    // Resolve tenant subdomain to UUID if needed
    let tenantUuid = tenantParam;
    if (
      !tenantParam.match(
        /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i
      )
    ) {
      const { data: tenantData, error: tenantError } = await serviceSupabase
        .from('tenants')
        .select('id')
        .eq('subdomain', tenantParam)
        .single();

      if (tenantError || !tenantData) {
        return NextResponse.json({ error: 'Invalid tenant' }, { status: 400 });
      }
      tenantUuid = tenantData.id;
    }

    // Verify user has access to this tenant
    const { data: user, error: userError } = await serviceSupabase
      .from('users')
      .select('id')
      .eq('clerk_id', userId)
      .eq('tenant_id', tenantUuid)
      .single();

    if (userError || !user) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 });
    }

    // Fetch ticket messages with author information and attachments
    const { data: messages, error: messagesError } = await serviceSupabase
      .from('ticket_messages')
      .select(
        `
        *,
        author:users!ticket_messages_author_id_fkey(
          id,
          first_name,
          last_name,
          email,
          avatar_url
        ),
        attachments(
          id,
          file_name,
          file_type,
          file_size,
          storage_path,
          created_at
        )
      `
      )
      .eq('ticket_id', ticketId)
      .eq('tenant_id', tenantUuid)
      .order('created_at', { ascending: true });

    if (messagesError) {
      console.error('Error fetching ticket messages:', messagesError);
      return NextResponse.json(
        { error: 'Failed to fetch messages' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      messages: messages || [],
    });
  } catch (error) {
    console.error('Error in ticket messages GET API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
