import { useState, useEffect, useCallback } from 'react';
import { useAuth } from '@clerk/nextjs';
import { useSupabaseClient } from '@/lib/supabase-clerk';
import { useTicketingStore } from '../store/use-ticketing-store';
import { cacheService } from '@/lib/cache/cache-service';
import { toast } from 'sonner';

export interface TicketMessage {
  id: string;
  tenant_id: string;
  ticket_id: string;
  author_id: string;
  content: string;
  message_type: 'message' | 'note' | 'status_change' | 'reply';
  is_internal: boolean;
  created_at: string;
  updated_at: string;
  author?: {
    id: string;
    first_name: string;
    last_name: string;
    email: string;
    avatar_url?: string | null;
  };
  attachments?: {
    id: string;
    file_name: string;
    file_type: string;
    file_size: number;
    storage_path: string;
    created_at: string;
  }[];
  isOptimistic?: boolean;
}

interface UseTicketMessagesReturn {
  messages: TicketMessage[];
  isLoading: boolean;
  error: string | null;
  sendMessage: (
    content: string,
    attachmentIds?: string[],
    messageType?: 'message' | 'note',
    uploadedFiles?: { file: File; id: string }[]
  ) => Promise<void>;
  refreshMessages: () => Promise<void>;
}

interface CachedMessage {
  id: string;
  tenant_id: string;
  ticket_id: string;
  author_id: string;
  content: string;
  message_type: 'message' | 'note' | 'status_change' | 'reply';
  is_internal: boolean;
  created_at: string;
  updated_at?: string;
  author_first_name?: string;
  author_last_name?: string;
  author_email?: string;
}

// Helper to transform cached message format to the required TicketMessage interface
const transformCachedMessages = (
  cachedMessages: CachedMessage[]
): TicketMessage[] => {
  return cachedMessages.map((cached) => ({
    id: cached.id,
    tenant_id: cached.tenant_id,
    ticket_id: cached.ticket_id,
    author_id: cached.author_id,
    content: cached.content,
    message_type: cached.message_type as
      | 'message'
      | 'note'
      | 'status_change'
      | 'reply',
    is_internal: cached.is_internal,
    created_at: cached.created_at,
    updated_at: cached.updated_at || cached.created_at,
    ...(cached.author_first_name && {
      author: {
        id: cached.author_id,
        first_name: cached.author_first_name,
        last_name: cached.author_last_name || '',
        email: cached.author_email || '',
      },
    }),
  }));
};

interface CurrentUserInfo {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
}

export function useTicketMessages(
  ticketId: string,
  currentUser?: CurrentUserInfo
): UseTicketMessagesReturn {
  const [messages, setMessages] = useState<TicketMessage[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [actualTenantId, setActualTenantId] = useState<string | null>(null);
  // Removed optimistic message IDs state - using Framer Motion animations instead

  const { getToken } = useAuth();
  const { currentTenantId } = useTicketingStore();
  const { supabase } = useSupabaseClient();

  // Effect to derive the actual tenant UUID from the subdomain
  useEffect(() => {
    if (!currentTenantId) {
      setActualTenantId(null);
      return;
    }
    if (currentTenantId.includes('-')) {
      setActualTenantId(currentTenantId);
      return;
    }

    let isCancelled = false;
    const fetchTenantUuid = async () => {
      try {
        const { data, error } = await supabase
          .from('tenants')
          .select('id')
          .eq('subdomain', currentTenantId)
          .single();
        if (error) throw error;
        if (!isCancelled) setActualTenantId(data.id);
      } catch (err) {
        console.error('Failed to fetch tenant UUID:', err);
        if (!isCancelled) setActualTenantId(null);
      }
    };
    fetchTenantUuid();
    return () => {
      isCancelled = true;
    };
  }, [currentTenantId, supabase]);

  // Helper function to make authenticated API calls with retry logic
  const makeAuthenticatedRequest = useCallback(
    async (
      url: string,
      options: RequestInit = {},
      retryCount = 0
    ): Promise<Response> => {
      const maxRetries = 1; // Only retry once for authentication errors

      try {
        const token = await getToken({ template: 'supabase' });
        const response = await fetch(url, {
          ...options,
          headers: {
            'Content-Type': 'application/json',
            ...(token && { Authorization: `Bearer ${token}` }),
            ...options.headers,
          },
        });

        // If authentication fails and we haven't retried yet, try once more
        if (response.status === 401 && retryCount < maxRetries) {
          console.warn('Authentication failed, attempting to refresh token...');
          // Exponential backoff: 1s, 2s, 4s...
          const delay = Math.pow(2, retryCount) * 1000;
          await new Promise((resolve) => setTimeout(resolve, delay));
          return makeAuthenticatedRequest(url, options, retryCount + 1);
        }

        return response;
      } catch (error) {
        if (retryCount < maxRetries) {
          console.warn('Request failed, retrying...', error);
          // Exponential backoff: 1s, 2s, 4s...
          const delay = Math.pow(2, retryCount) * 1000;
          await new Promise((resolve) => setTimeout(resolve, delay));
          return makeAuthenticatedRequest(url, options, retryCount + 1);
        }
        throw error;
      }
    },
    [getToken]
  );

  // Centralized data fetching logic
  const fetchMessages = useCallback(async () => {
    if (!actualTenantId || !ticketId) return;

    const startTime = Date.now();
    setIsLoading(true);
    setError(null);

    // Immediately clear messages for the new ticket to ensure the skeleton shows
    setMessages([]);

    const finishLoading = () => {
      const elapsedTime = Date.now() - startTime;
      const remainingTime = Math.max(0, 100 - elapsedTime);
      setTimeout(() => setIsLoading(false), remainingTime);
    };

    try {
      // Attempt to load from cache for a quick initial display
      const cachedMessages = await cacheService.getCachedTicketMessages(
        actualTenantId,
        ticketId
      );
      if (cachedMessages?.length > 0) {
        setMessages(transformCachedMessages(cachedMessages));
      }

      // Always fetch from the API to get the latest data and update the cache
      const response = await makeAuthenticatedRequest(
        `/api/tickets/${ticketId}/messages?tenant_id=${actualTenantId}`
      );

      if (!response.ok) {
        const errorData = await response.json();

        // Handle authentication errors specifically
        if (response.status === 401) {
          console.warn(
            'Authentication failed while fetching messages. Session may have expired.'
          );
          throw new Error('Authentication required. Please sign in again.');
        }

        throw new Error(errorData.error || 'Failed to fetch messages');
      }
      const data = await response.json();
      const apiMessages = data.messages || [];

      setMessages(apiMessages);
      await cacheService.cacheTicketMessages(
        actualTenantId,
        ticketId,
        apiMessages
      );
    } catch (err) {
      console.error('Error fetching ticket messages:', err);
      setError(
        err instanceof Error ? err.message : 'An unknown error occurred'
      );
    } finally {
      finishLoading();
    }
  }, [ticketId, actualTenantId, makeAuthenticatedRequest]);

  // Trigger fetch when ticketId or tenantId changes
  useEffect(() => {
    if (ticketId && actualTenantId) {
      fetchMessages();
    }
  }, [ticketId, actualTenantId, fetchMessages]);

  // Send a new message with optimistic UI
  const sendMessage = useCallback(
    async (
      content: string,
      attachmentIds: string[] = [],
      messageType: 'message' | 'note' = 'message',
      uploadedFiles?: { file: File; id: string }[]
    ) => {
      if (!actualTenantId || !ticketId || !content.trim()) return;

      // Create optimistic attachments from uploaded files
      const optimisticAttachments = (uploadedFiles || []).map(
        (uploadedFile) => ({
          id: uploadedFile.id,
          file_name: uploadedFile.file.name,
          file_type: uploadedFile.file.type,
          file_size: uploadedFile.file.size,
          storage_path: `temp/${uploadedFile.id}`,
          created_at: new Date().toISOString(),
        })
      );

      const optimisticMessage: TicketMessage & { isOptimistic?: boolean } = {
        id: `temp-${Date.now()}`,
        tenant_id: actualTenantId,
        ticket_id: ticketId,
        author_id: currentUser?.id || 'current-user',
        content: content.trim(),
        message_type: messageType,
        is_internal: messageType === 'note',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        author: {
          id: currentUser?.id || 'current-user',
          first_name: currentUser?.firstName || 'You',
          last_name: currentUser?.lastName || '',
          email: currentUser?.email || '',
        },
        attachments: optimisticAttachments,
        isOptimistic: true,
      };

      setMessages((prev) => [...prev, optimisticMessage]);

      try {
        const response = await makeAuthenticatedRequest(
          `/api/tickets/${ticketId}/messages`,
          {
            method: 'POST',
            body: JSON.stringify({
              content: content.trim(),
              tenant_id: actualTenantId,
              message_type: messageType,
              is_internal: messageType === 'note',
              attachment_ids: attachmentIds,
            }),
          }
        );

        if (!response.ok) {
          const errorData = await response.json();

          // Handle authentication errors specifically
          if (response.status === 401) {
            console.warn(
              'Authentication failed while sending message. Session may have expired.'
            );
            throw new Error('Authentication required. Please sign in again.');
          }

          throw new Error(errorData.error || 'Failed to send message');
        }

        const responseData = await response.json();

        // Update the optimistic message to remove the loading state
        setMessages((prev) =>
          prev.map((msg) =>
            msg.id === optimisticMessage.id
              ? { ...msg, isOptimistic: false }
              : msg
          )
        );

        // Show appropriate toast based on response (only for warnings)
        if (responseData.warning) {
          toast.error('Reply Sent with Issues', {
            description: responseData.warning,
            duration: 5000,
          });
        }
        // Note: Success toast is handled by the TicketDetail component
      } catch (err) {
        setMessages((prev) =>
          prev.filter((msg) => msg.id !== optimisticMessage.id)
        );
        const errorMessage =
          err instanceof Error ? err.message : 'Failed to send message';
        toast.error(errorMessage);
        throw err;
      }
    },
    [ticketId, actualTenantId, makeAuthenticatedRequest]
  );

  // Set up real-time subscription for new messages
  useEffect(() => {
    if (!actualTenantId || !ticketId || !supabase) return;

    const channel = supabase.channel(`ticket_messages_${ticketId}`);
    channel
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'ticket_messages',
          filter: `ticket_id=eq.${ticketId}`,
        },
        (payload) => {
          console.log('📢 Real-time update received:', payload);
          // Note: Removed automatic fetchMessages() to prevent unwanted refresh after optimistic UI updates
          // The optimistic UI will remain as the final state for better user experience
        }
      )
      .subscribe((status) => {
        if (status === 'SUBSCRIBED') {
          console.log(
            `✅ Subscribed to real-time updates for ticket ${ticketId}`
          );
        }
      });

    return () => {
      supabase.removeChannel(channel);
    };
  }, [ticketId, actualTenantId, supabase, fetchMessages]);

  return {
    messages,
    isLoading,
    error,
    sendMessage,
    refreshMessages: fetchMessages,
  };
}
