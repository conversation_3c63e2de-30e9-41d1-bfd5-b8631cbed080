import { createTenantClient } from '@/lib/supabase';
import { Ticket } from '@/features/ticketing/models/ticket.schema';
import { RoleBasedFilterContext } from '@/features/ticketing/types/role-based-filtering';

/**
 * Get tickets for a specific tenant
 */
export const getTickets = async (tenantId: string): Promise<Ticket[]> => {
  const tenantClient = createTenantClient(tenantId);

  try {
    return await tenantClient.getTickets();
  } catch (error) {
    console.error('Error fetching tickets:', error);
    return [];
  }
};

/**
 * Update a ticket (tenant-scoped)
 */
export const updateTicket = async (
  tenantId: string,
  ticketId: string,
  updates: Partial<Ticket>
): Promise<Ticket | null> => {
  const tenantClient = createTenantClient(tenantId);

  try {
    return await tenantClient.updateTicket(ticketId, updates);
  } catch (error) {
    console.error('Error updating ticket:', error);
    return null;
  }
};

/**
 * Create a new ticket (tenant-scoped)
 */
export const createTicket = async (
  tenantId: string,
  ticket: Omit<Ticket, 'id' | 'tenantId' | 'createdAt' | 'updatedAt'>
): Promise<Ticket | null> => {
  const tenantClient = createTenantClient(tenantId);

  try {
    return await tenantClient.createTicket(ticket);
  } catch (error) {
    console.error('Error creating ticket:', error);
    return null;
  }
};

/**
 * Get a specific ticket (tenant-scoped)
 */
export const getTicket = async (
  tenantId: string,
  ticketId: string
): Promise<Ticket | null> => {
  const tenantClient = createTenantClient(tenantId);

  try {
    return await tenantClient.getTicket(ticketId);
  } catch (error) {
    console.error('Error fetching ticket:', error);
    return null;
  }
};

/**
 * Delete a ticket (tenant-scoped)
 */
export const deleteTicket = async (
  tenantId: string,
  ticketId: string
): Promise<boolean> => {
  const tenantClient = createTenantClient(tenantId);

  try {
    await tenantClient.deleteTicket(ticketId);
    return true;
  } catch (error) {
    console.error('Error deleting ticket:', error);
    return false;
  }
};

/**
 * Get tickets with role-based filtering
 */
export const getTicketsWithRoleFilter = async (
  context: RoleBasedFilterContext,
  options?: {
    roleFilter?: 'assigned' | 'created' | 'all';
    status?: string[];
  }
): Promise<Ticket[]> => {
  try {
    const params = new URLSearchParams({
      tenant_id: context.tenantId,
      user_role: context.role,
      role_filter: options?.roleFilter || 'assigned',
    });

    if (options?.status && options.status.length > 0) {
      params.set('status', options.status.join(','));
    }

    const response = await fetch(`/api/tickets?${params.toString()}`);

    if (!response.ok) {
      throw new Error(`Failed to fetch tickets: ${response.statusText}`);
    }

    const tickets = await response.json();
    return tickets;
  } catch (error) {
    console.error('Error fetching role-based tickets:', error);
    return [];
  }
};

/**
 * Get assigned tickets for current user based on role
 */
export const getAssignedTickets = async (
  context: RoleBasedFilterContext
): Promise<Ticket[]> => {
  return getTicketsWithRoleFilter(context, {
    roleFilter: 'assigned',
    status: ['open', 'pending'],
  });
};

/**
 * Get all open tickets for current user based on role
 */
export const getOpenTickets = async (
  context: RoleBasedFilterContext
): Promise<Ticket[]> => {
  return getTicketsWithRoleFilter(context, {
    roleFilter: 'assigned',
    status: ['open'],
  });
};

/**
 * Assign a ticket to a user
 */
export const assignTicket = async (
  ticketId: string,
  assignedTo: string | null,
  tenantId: string,
  reason?: string
): Promise<{ success: boolean; message: string; ticket?: Ticket }> => {
  try {
    const response = await fetch(`/api/tickets/${ticketId}/assign`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        assigned_to: assignedTo,
        tenant_id: tenantId,
        reason,
      }),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.error || 'Failed to assign ticket');
    }

    const result = await response.json();
    return {
      success: result.success,
      message: result.message,
      ticket: result.ticket,
    };
  } catch (error) {
    console.error('Error assigning ticket:', error);
    return {
      success: false,
      message:
        error instanceof Error ? error.message : 'Failed to assign ticket',
    };
  }
};
