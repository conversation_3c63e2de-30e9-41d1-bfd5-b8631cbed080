'use client';

import { ProfileSection } from './sections/ProfileSection';
import { AppearanceSection } from './sections/AppearanceSection';
import { SecuritySection } from './sections/SecuritySection';
import { NotificationsSection } from './sections/NotificationsSection';
import { AdminSection } from './sections/AdminSection';
import { AssignmentSection } from './sections/AssignmentSection';

interface SettingsContentProps {
  activeSection: string;
  hasAdminAccess: boolean;
}

export function SettingsContent({
  activeSection,
  hasAdminAccess,
}: SettingsContentProps) {
  const renderSection = () => {
    switch (activeSection) {
      case 'profile':
        return <ProfileSection />;
      case 'appearance':
        return <AppearanceSection />;
      case 'security':
        return <SecuritySection />;
      case 'notifications':
        return <NotificationsSection />;
      case 'admin':
        return hasAdminAccess ? <AdminSection /> : null;
      case 'assignment':
        return hasAdminAccess ? <AssignmentSection /> : null;
      default:
        return <ProfileSection />;
    }
  };

  return <div className='space-y-6'>{renderSection()}</div>;
}
