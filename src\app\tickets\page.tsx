'use client';
import { useEffect } from 'react';
import Link from 'next/link';
import dynamic from 'next/dynamic';
import { AppLayout } from '@/features/shared/components/AppLayout';
import { RecentTickets } from '@/features/ticketing/components/RecentTickets';
import { useTicketWorkflow } from '@/features/ticketing/hooks/useTicketWorkflow';
import { useTicketData } from '@/features/ticketing/hooks/useTicketData';
import { useAuth, usePermissions } from '@/features/shared/hooks/useAuth';
import { useSettingsSync } from '@/features/settings/hooks/useSettingsSync';
import { VisitorInformation } from '@/features/visitor/components/VisitorInformation';
import { useTenantStore } from '@/features/tenant/store/use-tenant-store';
import { useTicketingSelectors } from '@/features/ticketing/store/use-ticketing-store';
import { AuthLoadingState } from '@/features/shared/components/AuthLoadingState';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  Di<PERSON>Header,
  DialogTitle,
} from '@/features/shared/components/ui/dialog';
import { Button } from '@/features/shared/components/ui/button';
import { Ticket } from '@/features/ticketing/models/ticket.schema';

const TicketDetail = dynamic(
  () =>
    import('@/features/ticketing/components/TicketDetail').then((mod) => ({
      default: mod.TicketDetail,
    })),
  {
    loading: () => (
      <div className='flex items-center justify-center h-64 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700'>
        <div className='text-center'>
          <div className='animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4'></div>
          <p className='text-gray-500 dark:text-gray-400'>
            Loading ticket details...
          </p>
        </div>
      </div>
    ),
    ssr: false,
  }
);

const CreateTicketForm = dynamic(
  () =>
    import('@/features/ticketing/components/CreateTicketForm').then((mod) => ({
      default: mod.CreateTicketForm,
    })),
  {
    loading: () => (
      <div className='flex items-center justify-center h-64 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700'>
        <div className='text-center'>
          <div className='animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4'></div>
          <p className='text-gray-500 dark:text-gray-400'>
            Loading create form...
          </p>
        </div>
      </div>
    ),
    ssr: false,
  }
);

function TicketsPageContent({ initialTickets }: { initialTickets: Ticket[] }) {
  const { user, isLoaded, role } = useAuth();
  const { hasPermission } = usePermissions();
  const { tenantId, domainInfo } = useTenantStore();

  // Initialize settings sync
  useSettingsSync();
  const { tickets, isLoading, isCacheLoaded, hasInitialApiLoad } =
    useTicketData(tenantId, initialTickets);
  const { updateTicket, setTenantId } =
    useTicketingSelectors.useTicketingActions();
  const {
    selectedTicket,
    isCreatingTicket,
    isSubmitting,
    showDraftConfirmation,
    handleCreateTicket,
    handleCancelCreateTicket,
    handleTicketSelect,
    handleSaveChanges,
    handleDiscardChanges,
    handleDialogClose,
    handleSubmitTicket,
  } = useTicketWorkflow(tickets, tenantId, isCacheLoaded, hasInitialApiLoad);
  const selectedTicketId = selectedTicket?.id ?? null;

  // Sync tenant ID between stores
  useEffect(() => {
    if (tenantId) {
      setTenantId(tenantId);
    }
  }, [tenantId, setTenantId]);

  // Show loading while authentication is being checked
  if (!isLoaded) {
    return (
      <AuthLoadingState
        isLoading={!isLoaded}
        message='Loading your workspace...'
      >
        <div />
      </AuthLoadingState>
    );
  }

  // For localhost, show welcome message if not authenticated
  if (domainInfo?.isLocalhost && !user) {
    return (
      <div className='min-h-screen flex items-center justify-center bg-gray-50'>
        <div className='max-w-md w-full space-y-8 p-8 text-center'>
          <h1 className='text-4xl font-bold text-gray-900 mb-4'>
            Welcome to QuantumNest
          </h1>
          <p className='text-lg text-gray-600 mb-8'>
            This is a multi-tenant support ticketing system. To access tickets,
            please visit your organization&apos;s subdomain or sign in.
          </p>
          <Link
            href='/sign-in'
            className='inline-block bg-blue-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-blue-700 transition-colors'
          >
            Sign In
          </Link>
        </div>
      </div>
    );
  }

  // For subdomains, authentication is required (handled by middleware)
  // If we reach here, user is authenticated

  return (
    <AppLayout rightSidebar={<VisitorInformation />}>
      <div className='flex h-full'>
        {/* Recent Tickets Sidebar */}
        <div className='w-96 shrink-0 h-full flex flex-col'>
          <div className='flex-1'>
            <RecentTickets
              selectedTicketId={selectedTicketId}
              onTicketSelect={handleTicketSelect}
              onCreateTicket={handleCreateTicket}
              tickets={tickets}
              isLoading={isLoading}
            />
          </div>
        </div>

        {/* Main Content - Ticket Detail or Create Form */}
        <div className='flex-1 px-6 h-full overflow-hidden'>
          {isCreatingTicket ? (
            <CreateTicketForm
              onSubmit={handleSubmitTicket}
              onDiscard={handleCancelCreateTicket}
              isSubmitting={isSubmitting}
              tenantId={tenantId || undefined}
            />
          ) : selectedTicket ? (
            <TicketDetail
              ticket={selectedTicket}
              onTicketUpdate={updateTicket}
            />
          ) : (
            <div className='flex-1 h-[calc(100%-3rem)] my-6 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm flex items-center justify-center'>
              <div className='text-center'>
                <p className='text-gray-500 dark:text-gray-400 mb-2'>
                  {tickets.length === 0
                    ? role === 'agent' || role === 'user'
                      ? 'No tickets assigned to you'
                      : 'No tickets available'
                    : 'Select a ticket to view details'}
                </p>
                {tickets.length === 0 && !hasPermission('tickets.create') && (
                  <p className='text-sm text-gray-400 dark:text-gray-500'>
                    Contact your administrator to get tickets assigned to you
                  </p>
                )}
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Draft Confirmation Dialog */}
      <Dialog open={showDraftConfirmation} onOpenChange={handleDialogClose}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Save Your Changes?</DialogTitle>
            <DialogDescription>
              You have unsaved changes in your ticket draft. What would you like
              to do?
            </DialogDescription>
          </DialogHeader>
          <DialogFooter className='gap-2'>
            <Button variant='outline' onClick={handleDiscardChanges}>
              Discard Changes
            </Button>
            <Button onClick={handleSaveChanges}>Save Changes</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </AppLayout>
  );
}

export default function TicketsPage() {
  return <TicketsPageContent initialTickets={[]} />;
}
