/**
 * HTML Sanitization Utilities
 *
 * Provides safe HTML rendering capabilities using DOMPurify to prevent XSS attacks
 * while allowing basic HTML formatting in ticket descriptions and content.
 *
 * <AUTHOR> Augster
 * @version 1.0 - Safe HTML Rendering (January 2025)
 */

import DOMPurify from 'dompurify';

/**
 * Configuration for DOMPurify sanitization
 * Allows basic formatting tags while preventing dangerous elements
 */
const SANITIZE_CONFIG = {
  ALLOWED_TAGS: [
    'p',
    'br',
    'strong',
    'b',
    'em',
    'i',
    'u',
    'span',
    'h1',
    'h2',
    'h3',
    'h4',
    'h5',
    'h6',
    'ul',
    'ol',
    'li',
    'blockquote',
    'a',
  ],
  ALLOWED_ATTR: ['style', 'class', 'href', 'target', 'rel'],
  ALLOWED_URI_REGEXP:
    /^(?:(?:(?:f|ht)tps?|mailto|tel|callto|cid|xmpp):|[^a-z]|[a-z+.\-]+(?:[^a-z+.\-:]|$))/i,
  FORBID_TAGS: [
    'script',
    'object',
    'embed',
    'form',
    'input',
    'textarea',
    'select',
    'button',
  ],
  FORBID_ATTR: [
    'onerror',
    'onload',
    'onclick',
    'onmouseover',
    'onfocus',
    'onblur',
  ],
  KEEP_CONTENT: true,
  RETURN_DOM: false,
  RETURN_DOM_FRAGMENT: false,
  RETURN_TRUSTED_TYPE: false,
};

/**
 * Sanitizes HTML content to prevent XSS attacks while preserving basic formatting
 *
 * @param html - Raw HTML string to sanitize
 * @returns Sanitized HTML string safe for rendering
 */
export function sanitizeHtml(html: string): string {
  if (!html || typeof html !== 'string') {
    return '';
  }

  // Remove any null bytes and normalize whitespace
  const cleanHtml = html.replace(/\0/g, '').trim();

  if (!cleanHtml) {
    return '';
  }

  // Sanitize the HTML using DOMPurify
  const sanitized = DOMPurify.sanitize(cleanHtml, SANITIZE_CONFIG);

  return sanitized;
}

/**
 * Strips all HTML tags from content, leaving only plain text
 * Useful for previews or when HTML rendering is not desired
 *
 * @param html - HTML string to strip
 * @returns Plain text without HTML tags
 */
export function stripHtml(html: string): string {
  if (!html || typeof html !== 'string') {
    return '';
  }

  // First sanitize to ensure safety, then strip all tags
  const sanitized = sanitizeHtml(html);
  return sanitized.replace(/<[^>]*>/g, '').trim();
}

/**
 * Checks if a string contains HTML tags
 *
 * @param content - String to check
 * @returns True if content contains HTML tags
 */
export function hasHtmlTags(content: string): boolean {
  if (!content || typeof content !== 'string') {
    return false;
  }

  return /<[^>]*>/g.test(content);
}

/**
 * Truncates HTML content to a specified length while preserving HTML structure
 *
 * @param html - HTML content to truncate
 * @param maxLength - Maximum length of plain text content
 * @returns Truncated HTML with proper tag closure
 */
export function truncateHtml(html: string, maxLength: number = 150): string {
  if (!html || typeof html !== 'string') {
    return '';
  }

  // First sanitize the HTML
  const sanitized = sanitizeHtml(html);

  // Get plain text length
  const plainText = stripHtml(sanitized);

  if (plainText.length <= maxLength) {
    return sanitized;
  }

  // Simple truncation - for more complex needs, consider using a dedicated library
  const truncatedText = plainText.substring(0, maxLength) + '...';

  // If original had HTML, wrap in paragraph tag
  if (hasHtmlTags(sanitized)) {
    return `<p>${truncatedText}</p>`;
  }

  return truncatedText;
}
