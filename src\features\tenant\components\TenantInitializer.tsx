'use client';

import { useEffect } from 'react';
import { useUser } from '@clerk/nextjs';
import { useTenantActions } from '../store/use-tenant-store';

export function TenantInitializer() {
  const { isLoaded } = useUser();
  const { initializeTenant } = useTenantActions();

  useEffect(() => {
    if (isLoaded) {
      initializeTenant();
    }
  }, [isLoaded, initializeTenant]);

  return null;
}
