'use client';

/**
 * Real-Time Ticket Hook - React 19 Performance Optimized
 *
 * This hook provides smart real-time functionality that only adds NEW content
 * and updates specific changed fields without touching existing data.
 *
 * Key Optimizations:
 * - React 19 performance patterns with optimized useCallback dependencies
 * - Smart incremental adds (only NEW tickets)
 * - Granular updates (only changed fields)
 * - Zero impact on existing UI state
 * - Element-level updates preserving UI state
 * - Production-ready error handling
 * - Optimized store selectors for minimal re-renders
 *
 * <AUTHOR> Augster
 * @version 3.0 - React 19 Performance Optimized (January 2025)
 */

import { useEffect, useCallback, useMemo } from 'react';
import { useSupabaseClient } from '@/lib/supabase-clerk';
import { useTicketingSelectors } from '../store/use-ticketing-store';
import type { Database } from '@/types/supabase';
import RealtimeDataService from '@/lib/services/realtime-data.service';
type TicketRow = Database['public']['Tables']['tickets']['Row'];

export function useTicketRealtime(
  tenantId: string | null,
  enabled: boolean = true
) {
  const { supabase, isAuthenticated } = useSupabaseClient();
  const { addTicketIncremental, updateTicketIncremental } =
    useTicketingSelectors.useTicketingActions();
  const realtimeDataService = useMemo(
    () => new RealtimeDataService(supabase),
    [supabase]
  );

  const isRealTimeEnabled = useMemo(
    () => enabled && !!tenantId && isAuthenticated,
    [enabled, tenantId, isAuthenticated]
  );

  const handleTicketInsert = useCallback(
    async (payload: TicketRow) => {
      const newTicket = await realtimeDataService.transformTicketRow(payload);
      addTicketIncremental(newTicket);
    },
    [addTicketIncremental, realtimeDataService]
  );

  const handleTicketUpdate = useCallback(
    async (payload: TicketRow) => {
      const updatedTicket =
        await realtimeDataService.transformTicketRow(payload);
      updateTicketIncremental(updatedTicket.id, {
        title: updatedTicket.title,
        description: updatedTicket.description,
        status: updatedTicket.status,
        priority: updatedTicket.priority,
        department: updatedTicket.department,
        updatedAt: updatedTicket.updatedAt,
      });
    },
    [updateTicketIncremental, realtimeDataService]
  );

  const handleTicketDelete = useCallback(
    (payload: TicketRow) => {
      updateTicketIncremental(payload.id, {
        status: 'closed' as const,
        updatedAt: new Date(),
      });
    },
    [updateTicketIncremental]
  );

  useEffect(() => {
    if (!isRealTimeEnabled) {
      return;
    }

    const channel = supabase
      .channel(`tickets:${tenantId}`)
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'tickets',
          filter: `tenant_id=eq.${tenantId}`,
        },
        (payload) => handleTicketInsert(payload.new as TicketRow)
      )
      .on(
        'postgres_changes',
        {
          event: 'UPDATE',
          schema: 'public',
          table: 'tickets',
          filter: `tenant_id=eq.${tenantId}`,
        },
        (payload) => handleTicketUpdate(payload.new as TicketRow)
      )
      .on(
        'postgres_changes',
        {
          event: 'DELETE',
          schema: 'public',
          table: 'tickets',
          filter: `tenant_id=eq.${tenantId}`,
        },
        (payload) => handleTicketDelete(payload.old as TicketRow)
      )
      .subscribe();

    return () => {
      supabase.removeChannel(channel);
    };
  }, [
    isRealTimeEnabled,
    tenantId,
    supabase,
    handleTicketInsert,
    handleTicketUpdate,
    handleTicketDelete,
  ]);

  return {
    isEnabled: isRealTimeEnabled,
  };
}
