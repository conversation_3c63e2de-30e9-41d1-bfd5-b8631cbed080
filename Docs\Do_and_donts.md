# ✅ **2025 Code Review & Optimization Checklist**

### 🖥 **Frontend (React/Next.js)**

1. 🚫 Avoid unnecessary **re-renders**
   - Check for missing `React.memo`, `useCallback`, and `useMemo` where components re-render too often.

2. 🚫 Avoid multiple API calls for the same data
   - Use **SWR** or **React Query** for caching and revalidation.

3. 🚫 Avoid sending large JSON responses to frontend
   - Fetch only required fields, not entire objects.

4. 🚫 Avoid putting logic directly in JSX (inline functions, heavy calculations).
   - Move to hooks or utility functions.

5. 🚫 Avoid deeply nested components and props drilling.
   - Use **Context API, Zustand, or Jotai** for state management.

6. 🚫 Avoid hardcoding values (like URLs, keys).
   - Use environment variables and configs.

7. 🚫 Avoid missing error boundaries.
   - Wrap components with React **Error Boundary** to prevent app crashes.

8. 🚫 Avoid CSS duplication or inline styles everywhere.
   - Use **TailwindCSS** or scoped CSS modules.

9. 🚫 Avoid not using **Server Components** for static data.
   - Move rendering logic to server wherever possible in Next.js.

10. 🚫 Avoid loading all components/pages at once.
    - Use **lazy loading and code splitting**.

---

### ⚙ **Backend (Node.js, Express, Laravel, etc.)**

1. 🚫 Avoid multiple DB calls for same data in a request.
   - Use **caching** with **Redis or Memcached**.

2. 🚫 Avoid long, unoptimized database queries.
   - Add **indexes** and avoid N+1 query issues.

3. 🚫 Avoid sending entire DB tables in APIs.
   - Limit API responses to needed data only.

4. 🚫 Avoid missing API rate limiting.
   - Add protection against too many requests.

5. 🚫 Avoid API endpoints that don’t validate user input.
   - Use validation libraries (like Joi, Yup, or built-in Laravel validation).

6. 🚫 Avoid not handling API errors properly.
   - Return proper error codes (400, 500) and messages.

7. 🚫 Avoid blocking operations in event loop.
   - Use async/await properly and avoid sync functions.

8. 🚫 Avoid exposing sensitive data (like passwords, tokens) in logs or responses.
9. 🚫 Avoid over-engineering service layers with too many unnecessary abstractions.

---

### 🌐 **API & Data Handling**

1. 🚫 Avoid duplicate API endpoints for similar use cases.
2. 🚫 Avoid sending sensitive data to frontend (like passwords, API keys).
3. 🚫 Avoid large payloads without pagination or filtering.
4. 🚫 Avoid inconsistent API responses (always follow same structure).
5. 🚫 Avoid GET requests that modify data (use POST/PUT/PATCH).

---

### 🧠 **General Coding Practices**

1. 🚫 Avoid duplicate code (apply **DRY principle**).
2. 🚫 Avoid complex functions doing too many things.
   - Follow **Single Responsibility Principle (SOLID)**.

3. 🚫 Avoid magic numbers or strings.
   - Use named constants.

4. 🚫 Avoid hard-to-read, overly long files (>300-500 lines).
   - Break into smaller reusable modules.

5. 🚫 Avoid unnecessary comments that don’t add value.
6. 🚫 Avoid tight coupling between modules.
   - Use dependency injection where possible.

7. 🚫 Avoid keeping unused files, functions, or dead code.
8. 🚫 Avoid pushing unformatted code.
   - Enforce **Prettier/ESLint** for consistent style.

9. 🚫 Avoid not testing critical flows.
   - Add unit tests and API tests (Jest, Cypress).

---

### 🔥 **Modern Practices to Enforce (2025 Standards)**

✅ Use **React Server Components** for static-heavy pages.
✅ Use **Edge Functions** for low-latency APIs (Vercel/Cloudflare).
✅ Use **Bun** or **Vite** for faster builds.
✅ Use **stale-while-revalidate caching** (SWR/React Query).
✅ Use **TypeScript strict mode** enabled.
✅ Use **GraphQL** or API filtering to fetch only what’s needed.

---

Perfect, I'll create a full 2025 checklist tailored to your tech stack:

- Next.js 15.3.5, React 19.1.0, Zustand, Zod, Supabase, React Hook Form, and TypeScript 5
- It will follow the same clear, structured format as your example
- It will include real TypeScript-based examples focused on common mistakes specific to your libraries
- And it will recommend plugins/tools (like ESLint rules, React DevTools, bundle analyzers) your AI should use to enforce quality and performance.

I’ll get started now and notify you once the customized checklist is ready.

# ✅ **2025 Code Review & Optimization Checklist (Next.js 15 / React 19)**

### 🖥 **Frontend (Next.js & React)**

1. 🚫 **Avoid unnecessary re-renders** – In React 19, the new compiler auto-memoizes many components (React Forget) so overusing manual optimizations can hurt performance. Use `React.memo` for pure components to skip re-renders when props haven’t changed, and use `useMemo`/`useCallback` for expensive calculations or stable function props. Keep state local to the component that needs it (don’t lift state too high) to prevent unrelated components from re-rendering. Also, prefer **uncontrolled forms** with libraries like React Hook Form – it registers inputs to the DOM and minimizes re-renders on typing. Avoid inline anonymous functions in JSX (they create new references each render); instead, define handlers with `useCallback`.

   **Bad:** Passing an inline function causes `Child` to re-render every time:

   ```tsx
   // Parent.tsx (Bad)
   export default function Parent() {
     return <Child onClick={() => console.log('Clicked!')} />;
   }
   ```

   **Good:** Use `useCallback` to provide a stable function reference:

   ```tsx
   // Parent.tsx (Good)
   import { useCallback } from 'react';
   export default function Parent() {
     const handleClick = useCallback(() => console.log('Clicked!'), []);
     return <Child onClick={handleClick} />;
   }
   ```

   In React 19+, only use `useMemo/useCallback` for truly expensive operations – the React compiler often handles routine memoization for you.

2. 🚫 **Avoid multiple API calls for the same data** – Don’t fetch the same resource in different components independently. Use a data cache like **SWR** or **React Query** to share and reuse fetch results across components. This prevents duplicate network calls and keeps data in sync. For example, SWR follows a “stale-while-revalidate” strategy: it serves cached data first and re-fetches in the background.

   **Bad:** Two components fetching the same endpoint separately (wastes network):

   ```tsx
   // ComponentA.tsx (Bad)
   useEffect(() => { fetch('/api/data').then(...); }, []);
   // ComponentB.tsx (Bad)
   useEffect(() => { fetch('/api/data').then(...); }, []);
   ```

   **Good:** Use SWR or React Query to cache the result so the data is fetched once:

   ```tsx
   import useSWR from 'swr';
   const fetcher = (url) => fetch(url).then((res) => res.json());
   // ComponentA.tsx / ComponentB.tsx (Good)
   const { data, error } = useSWR('/api/data', fetcher);
   if (error) return <p>Error loading data.</p>;
   if (!data) return <p>Loading...</p>;
   // ...use data (will be shared from cache if already fetched)
   ```

   This way, both components use the same cached response instead of making two calls. **Tip:** With Next.js, consider fetching data in **Server Components or `getServerSideProps`** for better caching and to avoid redundant client requests.

3. 🚫 **Avoid sending large JSON payloads to the frontend** – Fetch only the required fields, not entire objects. Over-fetching wastes bandwidth and slows your app. Implement **filtering and pagination** so that you never return huge datasets in one response. If you use GraphQL or Supabase, request only the specific columns you need (GraphQL inherently prevents over-fetching).

   **Bad:**

   ```http
   GET /api/users  ➞ returns [{"id":1,"name":"Alice","email":"<EMAIL>","password":"$2b$10$..."}, ...]  // entire user objects, including sensitive fields!
   ```

   **Good:** Limit the data sent:

   ```http
   GET /api/users?fields=id,name,avatar&limit=50  ➞ returns [{"id":1,"name":"Alice","avatar":"..."}, ...]
   ```

   Or use GraphQL to fetch only needed fields:

   ```graphql
   query {
     users(limit: 50) {
       id
       name
       avatar
     }
   }
   ```

   This **reduces payload size** and protects sensitive data. _Never_ send secrets (passwords, API keys, etc.) in API responses or front-end code.

4. 🚫 **Avoid heavy logic inside components (especially in JSX)** – Don’t perform expensive calculations or data transformations during render. This can freeze the UI and triggers more work on every re-render. Instead, move complex logic to **hooks or utility functions**, or use `useMemo` to memoize results. Likewise, avoid defining large components _within_ other components – define them outside so they aren’t recreated every render.

   **Bad:** Doing expensive work in JSX on each render:

   ```tsx
   function ProductsList({ products }) {
     // Bad: sorting array during every render
     const listItems = products
       .sort(sortByPrice)
       .map((p) => <li key={p.id}>{p.name}</li>);
     return <ul>{listItems}</ul>;
   }
   ```

   **Good:** Use `useMemo` to only sort when `products` changes:

   ```tsx
   function ProductsList({ products }) {
     const sorted = useMemo(() => [...products].sort(sortByPrice), [products]);
     return (
       <ul>
         {sorted.map((p) => (
           <li key={p.id}>{p.name}</li>
         ))}
       </ul>
     );
   }
   ```

   Also, avoid inline computations in JSX; pre-calculate outside the return. This keeps render functions fast and focused on UI.

5. 🚫 **Avoid deeply nested components and prop drilling** – Passing props through multiple layers just to reach a deep child makes components tightly coupled and hard to maintain. Instead, use the **Context API** or a global state library like Zustand to provide data to distant components without threading props through every level. Zustand (or Redux, etc.) can manage global state accessible anywhere, eliminating prop drilling entirely.

   **Bad:** Prop drilling through unnecessary layers:

   ```tsx
   // Grandchild needs user, but Parent and Child only pass it along
   function Parent() {
     return <Child user={user} />;
   }
   function Child({ user }) {
     return <Grandchild user={user} />;
   }
   function Grandchild({ user }) {
     return <p>Hello, {user.name}</p>;
   }
   ```

   **Good:** Use React Context or Zustand store:

   ```tsx
   // Using Context API
   <UserContext.Provider value={user}>
     <Parent />{' '}
     {/* any deep component can use useContext(UserContext) to get user */}
   </UserContext.Provider>
   ```

   This way intermediate components don’t need to pass along data they don’t use. For Zustand, structure your store state into slices, and have components select only the slice they need. **Use shallow equality** for Zustand selectors to prevent unrelated state changes from re-rendering components.

6. 🚫 **Avoid hardcoding values (URLs, keys, config)** – Don’t scatter magic constants in your code. Hardcoded API URLs, secret keys, or IDs make code less flexible and pose security risks. Use **environment variables** or configuration files for values that may change per environment (development, production). This allows changes without code edits, and keeps sensitive data out of your source code (e.g. API keys should live in `.env` files, not in the repo).

   **Bad:**

   ```tsx
   const API_BASE_URL = 'https://myapp.com/api/v1'; // hardcoded URL
   const STRIPE_KEY = 'sk_live_51H...'; // sensitive key in code
   ```

   **Good:**

   ```tsx
   const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL;
   const stripe = new Stripe(process.env.STRIPE_SECRET_KEY);
   ```

   By externalizing config, you adhere to the _12-factor app_ principles and keep secrets safe. No magic numbers/strings – give them meaningful constant names or use enums so their purpose is clear.

7. 🚫 **Avoid missing error boundaries** – Always catch React runtime errors in the UI so they don’t break the entire app. Wrap key components or routes with a **React Error Boundary** to gracefully handle exceptions. This prevents the “white screen of death” by showing a fallback UI instead of crashing.

   **Bad:** If `<ProfilePage>` throws an error, the whole app crashes:

   ```tsx
   function App() {
     return <ProfilePage />; // no error boundary here
   }
   ```

   **Good:** Wrap with an error boundary:

   ```tsx
   import { ErrorBoundary } from 'react-error-boundary';
   function App() {
     return (
       <ErrorBoundary fallback={<ErrorFallback />}>
         <ProfilePage />
       </ErrorBoundary>
     );
   }
   ```

   Now, if `ProfilePage` or any child throws, the `ErrorFallback` UI is shown instead of a full crash. Remember to place boundaries around independent parts of the app (e.g. per route or widget). Log the errors in `componentDidCatch` or the error boundary’s `onError` callback to monitor issues.

8. 🚫 **Avoid CSS duplication or excessive inline styles** – Don’t repeat the same styles in multiple places or sprinkle inline styles everywhere; this leads to inconsistency and bloated markup. Use a centralized styling approach: **Tailwind CSS** (utility classes), **CSS Modules**, or styled-components to keep styles maintainable. These options help reuse styles and avoid conflicts (CSS Modules auto-scope class names). Inline styles should be minimal – they bypass CSS optimizations and are hard to override (plus they can’t handle media queries, etc.). Large teams often forbid inline styles as they are considered unmaintainable.

   **Bad:** Duplicated inline styles:

   ```jsx
   <div style={{ color: 'red', fontWeight: 'bold' }}>Error: Invalid input</div>
   <span style={{ color: 'red', fontWeight: 'bold' }}>Error: Try again</span>
   ```

   **Good:** Use a shared CSS class or utility:

   ```jsx
   {/* Using Tailwind utility classes */}
   <div className="text-red-600 font-bold">Error: Invalid input</div>
   <span className="text-red-600 font-bold">Error: Try again</span>
   {/* Or using a CSS module or global class */}
   <div className={styles.errorText}>Error: Invalid input</div>
   ```

   This ensures consistency and easier updates (change one class/style, not dozens of inline occurrences). It also keeps your JSX cleaner. **Tip:** Leverage CSS frameworks or modules to avoid specificity wars and make styling more systematic.

9. 🚫 **Avoid not using Server Components for static data** – Next.js 13+ (App Router) defaults to **React Server Components (RSC)** which render on the server by default. Embrace this for non-interactive parts of your UI. If a component doesn’t need access to browser APIs or state, let it remain a server component – this offloads work to the server, reduces bundle size, and can improve SEO. Don’t unnecessarily mark components with `\"use client\"` if they only display static or fetched data. By rendering on the server, you can take advantage of data caching and avoid shipping large JSON to the client. For example, fetch data in an RSC (or use Next’s `getStaticProps`/`getServerSideProps`) so the HTML is pre-filled with content. Use client components only for interactive parts (forms, event handlers, browser-only APIs). In short, render what you can on the **server side** (or at build time) to optimize performance and user-perceived load time.

   _Example:_ In Next.js App Router, your page components are server components by default. You might fetch blog posts in a server component and render them as HTML, instead of fetching on the client after load. This way the user sees content instantly and you avoid an extra client round-trip.

10. 🚫 **Avoid loading all components or code at once** – Take advantage of **code splitting** and lazy loading. Don’t bundle rarely-used components into the initial load. Use dynamic `import()` or React.lazy for components that can load on demand (e.g. modals, heavy charts on secondary pages). Next.js supports dynamic imports with optional SSR, and by default splits each route into a separate chunk. Ensure you’re not importing a huge library in the top-level scope of every page – import it dynamically inside a component or use lazy loading so it’s only loaded when needed.

    **Bad:** Importing a heavy component unconditionally:

    ```tsx
    import HeavyChart from '@/components/HeavyChart';
    export default function Dashboard() {
      // HeavyChart code loads even if not immediately used
      return <div>{showChart ? <HeavyChart /> : null}</div>;
    }
    ```

    **Good:** Dynamically load the component when needed:

    ```tsx
    import dynamic from 'next/dynamic';
    const HeavyChart = dynamic(() => import('@/components/HeavyChart'), {
      ssr: false,
    });
    export default function Dashboard() {
      return <div>{showChart ? <HeavyChart /> : null}</div>;
    }
    ```

    Or using React.lazy:

    ```tsx
    const HeavyChart = React.lazy(() => import('@/components/HeavyChart'));
    // ...
    <Suspense fallback={<p>Loading chart...</p>}>
      {showChart && <HeavyChart />}
    </Suspense>;
    ```

    This way, code for `HeavyChart` is not included in the main bundle, improving initial load. Also apply this principle to routes: use Next.js **dynamic routes** and **link prefetching** so users download code for a page only when they navigate to it. Use the Next.js Bundle Analyzer to ensure your bundles are optimized – e.g., no page is unnecessarily pulling in huge dependencies upfront.

---

### ⚙ **Backend (Supabase & Serverless)**

1. 🚫 **Avoid multiple database calls for the same data in one request** – If your server (or serverless function) needs the same data twice, fetch it once and reuse it. Even better, implement caching for frequent queries. Use an in-memory cache or an external cache like **Redis** or **Supabase’s built-in caching** to store results of expensive queries so subsequent requests can retrieve data quickly. This reduces load on the database and speeds up responses. Supabase functions or Next.js API routes should not query the DB repeatedly for identical data – fetch once and reuse, or batch queries together.

   _Example:_ If you need user info and user’s posts, avoid querying the users table and posts table separately if you can do a join or use a single RPC. And if dozens of requests ask for the same reference data, consider caching it (e.g., application configuration, lookup tables).

2. 🚫 **Avoid long, unoptimized database queries** – N+1 queries and full table scans will kill performance. Ensure your DB queries are optimized: use proper **indexes** on columns you filter or join by, and avoid the N+1 query problem by joining or using ORM eager-loading to get related data in one go. Supabase’s Postgres under the hood benefits from thoughtful SQL: use EXPLAIN to analyze query performance, and add indices (e.g. on foreign keys) to speed up reads. Also, only select the fields you need (don’t `SELECT *` if not necessary).

   **Bad:** Fetching posts in a loop for each user (classic N+1 problem):

   ```js
   const users = await db.users.findMany();
   for (const user of users) {
     user.posts = await db.posts.findMany({ where: { userId: user.id } });
   }
   // This executes 1 + N queries (N = number of users):contentReference[oaicite:47]{index=47}
   ```

   **Good:** Use a single optimized query or ORM relation:

   ```sql
   SELECT users.*, posts.*
   FROM users
   LEFT JOIN posts ON posts.user_id = users.id;
   ```

   Or in Supabase (JS):

   ```js
   const { data } = await supabase
     .from('users')
     .select('id, name, posts(id, title)');
   ```

   This retrieves users and their posts in one call. Add indexes on `posts.user_id` etc. to ensure these joins are fast. **Note:** Supabase by default will send all rows if you don’t limit – always use `.limit()` or proper filtering to avoid giant result sets.

3. 🚫 **Avoid sending entire DB tables in APIs** – Don’t expose large datasets directly. Always **filter** and **limit** results in your API endpoints. If an admin needs all data, implement pagination (e.g., 100 records at a time). This not only improves performance but also avoids overwhelming the client or network. In Supabase queries, use `.range()` or `.limit()` for pagination. Additionally, implement access control (row level security or application-side checks) so clients can’t fetch data they shouldn’t see.

   _Example:_ If you have 10,000 products, an endpoint `GET /products` should not return all 10k at once. Instead, support `GET /products?limit=50&offset=0` or cursor-based pagination. This principle keeps response times and payload sizes in check.

4. 🚫 **Avoid missing API rate limiting** – Any public-facing API (including Next.js API routes) should be protected against spam or abuse. Implement **rate limiting** to prevent a single client from overwhelming your server (e.g., limit requests per IP or API key). Use libraries or middleware to throttle requests (e.g., Express-rate-limit for Express, or use Vercel/Cloudflare settings for serverless). This is crucial for security (mitigating DDoS, brute force attacks) and stability. If using Supabase’s REST API or gotrue, consider their rate limits and add extra checks if needed.

   _Example:_ Allow at most 100 requests per minute per IP for certain endpoints. Return HTTP 429 Too Many Requests when exceeded. This ensures your backend isn’t abused by scripts or misbehaving clients.

5. 🚫 **Avoid endpoints that don’t validate user input** – All API endpoints should **validate and sanitize** inputs (server-side) before using them. Never trust client-side validation alone (e.g., React Hook Form on the client). Use a schema validation library like **Zod** (great for TypeScript), **Yup/Joi**, or built-in validators to enforce correct data shapes and types. This prevents bad data from causing errors or security issues (like SQL injection or crashes). If using Supabase RPC or Postgres functions, also validate inputs there or via constraints.

   **Bad:**

   ```js
   // No validation – trusting whatever comes in (dangerous)
   app.post('/signup', async (req, res) => {
     const { email, password } = req.body;
     // directly use email/password...
   });
   ```

   **Good:**

   ```ts
   import * as z from 'zod';
   const signupSchema = z.object({
     email: z.string().email(),
     password: z.string().min(8),
   });
   app.post('/signup', async (req, res) => {
     const parsed = signupSchema.safeParse(req.body);
     if (!parsed.success) {
       return res.status(400).json({ error: parsed.error.errors });
     }
     const { email, password } = parsed.data;
     // proceed with safe, validated data
   });
   ```

   By validating inputs, you catch mistakes or attacks early. This should be done on **all** inputs (query params, body, headers, etc.) as appropriate.

6. 🚫 **Avoid not handling API errors properly** – Always return appropriate HTTP status codes and error messages for exceptional cases. Clients should not receive a generic 200 on failure or a stack trace. Follow REST conventions for errors: e.g., **400** for bad requests (invalid input), **401** for unauthorized, **403** for forbidden, **404** for not found, **500** for server errors, etc.. Provide a useful error response body (message, maybe an error code) but do **not** leak sensitive info (like internals or SQL errors). Consistent error handling makes your API easier to use and debug.

   _Example:_ If a request fails validation, respond with `400 Bad Request` and JSON like `{"error":"Invalid email format"}`. Don’t just `console.log` the error and send a 200 or empty response. Use try/catch around logic to catch exceptions and respond with a 500 if something unexpected goes wrong. Proper error handling will save you and your clients a lot of pain.

7. 🚫 **Avoid blocking operations in the event loop (Node.js)** – In a Node or serverless environment, do not perform CPU-intensive or long-running tasks on the main thread. This will block the event loop and degrade throughput. Examples to avoid: synchronous file system calls (`fs.readFileSync`), heavy JSON parsing in one go, large in-memory loops without async breaks, or calling expensive crypto operations on the main thread. Instead, use **async APIs** and offload heavy work to worker threads or separate services.

   **Bad (Node.js API route):**

   ```js
   // Synchronous blocking code - BAD
   const data = fs.readFileSync('/large/file.csv');
   processData(data); // CPU-heavy parsing
   res.send('Done');
   ```

   During the file read and processing, no other request can be handled (event loop blocked).

   **Good:**

   ```js
   // Non-blocking alternative - GOOD
   fs.readFile('/large/file.csv', (err, data) => {
     if (err) return next(err);
     // process data in smaller chunks or use a worker thread for heavy CPU work
     processInWorker(data).then((result) => res.send('Done'));
   });
   ```

   Use libraries like **worker_threads** or job queues for CPU-bound tasks. For high-traffic systems, this is critical to keep the service responsive. Also prefer database streaming or pagination for large data rather than reading all into memory at once.

8. 🚫 **Avoid exposing sensitive data in logs or responses** – Don’t log secrets, PII, or any sensitive info in plaintext logs. Mask or omit things like passwords, tokens, credit card numbers, etc. Logging sensitive data can lead to security leaks if logs are compromised. For example, never `console.log(req.body.password)` on signup. Use hashing for any secrets and consider log sanitizers for removing PII. Similarly, never send internal implementation details in error responses (stack traces or config values). Follow the principle of least privilege with data: each system and user should see only what they need.

   _Example:_ Instead of logging a user’s full JWT or password, log an ID or partial info that can help debugging without compromising security. Many logging libraries support **redaction** rules to automatically strip out sensitive fields – use them in production.

9. 🚫 **Avoid over-engineering service logic** – Keep your backend code simple and focused on requirements. Don’t introduce needless abstraction layers, classes, or patterns that aren’t providing clear value (you ain’t gonna need it – **YAGNI**). Over-abstracting (factoring code into too many layers or abstract classes) can make code harder to follow and maintain. Strive for **clear, straightforward code** over clever indirection. Follow SOLID principles sensibly, but don’t apply patterns just for the sake of it. Each service or module should have a **single responsibility** (do one thing well), and you should aim for **loose coupling** – e.g., use dependency injection or interfaces to decouple modules, but don’t complicate if not necessary.

   _Guidance:_ If you have a thin controller calling a service, which calls a repository, which calls a DB client – ensure each layer is truly needed. Maybe the extra service layer can be removed if it’s just pass-through. Avoid creating generic “one-size-fits-all” solutions for a problem you don’t yet have. Write the simplest code that works, then refactor when requirements grow. This prevents analysis paralysis and overly complex designs.

---

### 🌐 **API & Data Handling**

1. 🚫 **Avoid duplicate API endpoints for similar use cases** – Don’t create separate endpoints that do the same thing. It causes code duplication and maintenance headaches. Instead, make endpoints flexible via query parameters or path params. For example, if you find `/getUsers` and `/listUsers` both return users, unify them. A clean REST API uses nouns and HTTP methods (e.g., `GET /users` for listing, `GET /users/{id}` for detail) rather than multiple verbs doing similar jobs. Consolidating endpoints makes your API easier to understand and evolve.

2. 🚫 **Avoid sending sensitive data to the frontend** – Similar to backend logging, ensure that your API responses never include secrets or data the client shouldn’t see. This includes user passwords (hashed or not), internal IDs or keys, and any data from other users. Always filter out or omit secure fields in your API serializers. In a multi-tenant app, enforce checks so one user cannot fetch another user’s data by tweaking an ID (authorization checks). Use role-based access control or row-level security (if using Supabase/Postgres) to enforce data privacy.

   _Example:_ If an admin endpoint returns user details, exclude fields like `password_hash`, `SSN`, etc. The frontend likely doesn’t need these, and exposing them is a serious risk. Design API response schemas to include only what’s necessary.

3. 🚫 **Avoid large payloads without pagination or filtering** – (Reiterating from frontend/backend) Always page through data if it can grow large. Also allow filtering on the server side (query params for search criteria) so clients don’t have to fetch everything and filter in JS. If you send 10MB of JSON to the browser but only display 50 items, that’s a huge inefficiency. Use query parameters or request bodies to let the client ask for only what it needs (e.g., `/orders?start=2023-01-01&end=2023-06-30` to get orders in a date range). This keeps your API fast and responsive as data grows.

4. 🚫 **Avoid inconsistent API responses** – Stick to a consistent structure for similar responses. For instance, always return an object with either a data field or an error field (don’t sometimes return an array, other times an object). If you use JSON\:API or another spec, follow it throughout. Inconsistency makes client code complex and error-prone. Also, maintain consistent **naming conventions** (e.g., use snake_case or camelCase for keys, but don’t mix both). Consistency helps both your team and consumers of your API.

5. 🚫 **Avoid using GET requests for operations that modify data** – GET (and HEAD) should be safe, side-effect-free requests. Don’t abuse GET for actions like `GET /approveUser?id=123` which changes state. Use the proper HTTP methods: **POST** for creating, **PUT/PATCH** for updating, **DELETE** for deleting. This is not just academic RESTfulness – it impacts how browsers, proxies, and caches handle your requests. Unsafe operations via GET can be accidentally cached or prefetched, causing unintended behavior. Keep your API semantics correct: GET for reads, POST for actions/commands that change state.

---

### 🧠 **General Coding Practices**

1. 🚫 **Avoid duplicate code (DRY principle)** – Don’t repeat yourself in code. If you find the same logic in multiple places, abstract it into a function or component. Duplication increases the chance of bugs and inconsistencies. Aim for each piece of knowledge or functionality to be defined once. But balance DRY with readability – sometimes very tiny abstractions can hurt clarity. Use good judgment, and refactor copy-paste as the codebase matures.

2. 🚫 **Avoid complex functions that do too many things** – Functions (or methods) should ideally have a single responsibility. If you struggle to name a function because it does A, B, and C, it likely needs to be split. Break large functions (or large React components) into smaller, focused units. This improves reuse and testing. A good function should fit on a screen (\~50 lines or less, as a rough guide) and perform one task or a coherent set of tasks. This is related to the **Single Responsibility Principle (SOLID)** – each module should have one reason to change.

3. 🚫 **Avoid magic numbers or strings** – Any unexplained constant in code is a “magic number.” Use **named constants** or enums to give these values context. For example, use `MAX_RETRIES = 3` instead of just `3` scattered around. This makes code self-documenting and easier to update. The same goes for string literals that have special meaning – e.g., status codes, role names – define them in a constants file or config. This practice improves readability and reduces errors (e.g., typos in repeated strings).

   **Bad:** `if (status === 2) {...}` – (What is 2?!)
   **Good:** `if (status === STATUS_APPROVED) {...}` – readers know 2 means “approved”.

   Comments or docs can lie or become outdated, but a well-named constant makes the code intention clear.

4. 🚫 **Avoid overly long files (e.g., >300-500 lines)** – Large files can indicate that it should be modularized. Split your code into multiple files or modules logically (by feature or functionality). For example, instead of one 1000-line file with all functions, group related functions into files (perhaps by domain: `authService.ts`, `userController.ts`, etc.). Shorter files are easier to navigate and understand. Most modern codebases keep files under a few hundred lines for clarity. If a single React component file grows huge, consider breaking part of the UI into child components. This also enforces better separation of concerns.

5. 🚫 **Avoid unnecessary comments that don’t add value** – Comments should explain _why_ the code does something, not _what_ the code is doing. Don’t write redundant comments that restate the code (“// increment i by 1” on a `i++` line – pointless). Instead, use descriptive variable and function names so the code is self-explanatory. Clean code is often self-documenting. Reserve comments for clarifying complex logic, rationale, or warnings (“TODO”s, workaround explanations, etc.). Remember Uncle Bob’s maxim: “Comments are sometimes a failure to express intent in code.” Strive to write clear code and reduce reliance on comments, except where they genuinely enhance understanding.

6. 🚫 **Avoid tight coupling between modules** – Modules or classes that are too tightly coupled (dependent on each other’s details) make maintenance difficult. Aim for **loose coupling**: each module should interact with others through clear interfaces or contracts. For instance, use dependency injection to pass in a dependency rather than hard-coding it inside a class – this allows you to swap implementations (e.g., for testing or upgrades) without changing the module code. Event emitters, callback interfaces, or pub-sub patterns can also decouple components. The goal is to reduce the ripple effect of changes: modifying module A should ideally not break module B. If you notice changing one file requires many changes elsewhere, consider refactoring to decouple. High cohesion (module’s internals belong together) and low coupling (minimal knowledge of other modules) lead to more robust code.

7. 🚫 **Avoid keeping unused files, functions, or dead code** – Remove code that is not being used. Dead code adds noise and confusion. It might also introduce latent bugs if it’s unintentionally invoked. Use your linter or IDE to find unused variables and functions. If you think code might be needed later, it’s often better to delete it now (you can retrieve it from version control if needed) – **YAGNI** principle applies here too. A clean codebase contains only what’s necessary for current functionality.

8. 🚫 **Avoid pushing unformatted code** – Consistent code style is crucial for readability. Use **Prettier** and **ESLint** (with appropriate configs for your stack: React, TypeScript, etc.) to automatically format code and catch style issues. Set up a pre-commit hook or CI check to run these. This prevents trivial diffs and makes the codebase feel cohesive. Ensure things like indentation, quotes, semicolon usage, etc., are standardized by your linter/formatter. Also enable **ESLint rules** for best practices (e.g., no unused vars, no implicit `any`, exhaustive deps for React hooks, etc.). A consistent style means developers (and AI assistants) can focus on logic rather than stylistic issues.

9. 🚫 **Avoid not testing critical flows** – If it’s not tested, it often doesn’t work. Ensure you have **unit tests** for pure functions and critical logic, and **integration/end-to-end tests** for user flows and API endpoints. Aim for a high coverage on core business logic (though 100% coverage is not a goal by itself, around \~80% is a common target). Use tools like **Jest** or **Vitest** for unit tests, and **Cypress or Playwright** for end-to-end tests of your app’s UI and APIs. Write tests for things like authentication, form validation, and important state transitions – catching bugs here prevents regressions in the future. Also, test edge cases and error conditions (not just the sunny day scenarios). Having a robust test suite will give you confidence to refactor and optimize without breaking functionality.

   _Note:_ Since you use TypeScript, some might argue types are a form of testing – they do prevent a class of errors, but they don’t assert behavior. So still write tests for the logic!

---

### 🔥 **Modern Practices to Enforce (2025 Standards)**

✅ **Use React Server Components and Server Actions** – Leverage Next.js 15’s latest features to run as much logic on the server as possible. Static-heavy pages or non-interactive UI should be server components for performance and SEO. Next.js Server Actions (if available) let you mutate data on the server seamlessly – use them to keep heavy lifting off the client. This reduces bundle size and improves time-to-hydration.

✅ **Use Edge Functions for low-latency, globally distributed APIs** – Deploy serverless functions to the edge (Vercel Edge Functions, Cloudflare Workers) to run code geographically close to users. This drastically lowers latency for users around the world. For example, authentication or feature toggles can run at the edge, returning responses in a few milliseconds. Edge Functions are ideal for lightweight requests that benefit from being ultra fast and globally distributed.

✅ **Use faster build tools (Bun, Vite)** – Traditional Webpack builds can be slow. By 2025, tools like **Bun** (an all-in-one JS runtime) or **Vite** (next-gen bundler/dev server) can significantly speed up development and build times. Bun’s dev server can start up to 3x faster than Node’s, and it bundles with esbuild under the hood for speed. Vite leverages ES modules and bundlers like Rollup for efficient builds and nearly instant HMR. Upgrading to these can save developer time and make CI/CD faster. (Next.js 15 may still use Webpack, but keep an eye on Vercel’s Rust-based Turbopack or Bun integration in the future.)

✅ **Use stale-while-revalidate caching strategies** – Implement caching both on the client (with SWR/React Query) and server (HTTP caching, revalidation headers). SWR (stale-while-revalidate) gives users instant data from cache and then updates in the background, improving UX. On Next.js, use `getStaticProps` with `revalidate` or Cache-Control headers to let CDN/browser cache pages and revalidate in the background. This hybrid approach yields fast and up-to-date experiences. Basically, **cache aggressively, update asynchronously**.

✅ **Use TypeScript strict mode** – Always enable `\"strict\": true` in your TS config, along with strict flags like `noImplicitAny`, `strictNullChecks`, etc. This ensures the compiler catches potential bugs (undefined cases, type mismatches) early. Embrace the type system fully – it will guide you to write safer code. By 2025, TypeScript 5+ has advanced type features; use them (e.g., `satisfies` operator, template literal types) to make your types robust. Don’t use `any` unless absolutely necessary (and if so, consider refining it). A strictly-typed codebase is easier to maintain and refactor confidently.

✅ **Use GraphQL or refined APIs to avoid over-fetching** – If your project can benefit from GraphQL (especially with Supabase which offers a GraphQL endpoint), use it to let clients request exactly the data they need. This eliminates over-fetching and under-fetching issues. If GraphQL is overkill, at least design REST endpoints with filtering and field selection (many REST APIs allow a `fields` query param). The idea is to minimize payloads and tailor responses to client needs for performance. In 2025, tools like **tRPC** or **GraphQL Codegen** can help create end-to-end type-safe APIs which align perfectly with your front-end queries.

---

# 📝 **Tell Your AI to Do This:**

✅ **“Check every file and ensure none of these mistakes exist.”** – Instruct the AI (or your development team) to systematically review the codebase against **each item** in this checklist. For example, verify that there are no unneeded re-renders (perhaps by using React DevTools Profiler to spot wasted renders), no duplicate API calls, no leaked secrets, etc. This is a comprehensive audit.

✅ **“Apply 2025 best practices and ensure maximum performance.”** – Have the AI suggest or implement changes following the above modern best practices. This could mean refactoring class components to functional with hooks, enabling React 19 features, using Next.js 15 App Router optimally, adding caching layers, etc. The goal is to update the project to current standards for speed, security, and maintainability.

✅ **“Remove redundant code, optimize queries, and follow DRY, SOLID, YAGNI principles.”** – The AI should delete or consolidate any duplicate code. It should analyze database queries (for Supabase, maybe translate them to optimized SQL/RPC calls) and suggest indexes or batching where necessary. It should enforce the DRY principle (no copy-paste code), Single Responsibility (each module does one thing), and YAGNI (no code for hypothetical future requirements). Essentially, clean up and simplify the architecture while keeping it extensible.

✅ **“Run static analysis and linters to catch issues.”** – Instruct the AI to use ESLint (with TypeScript and React plugins, including rules for hooks, accessibility, etc.) to identify problems like unused variables, improper dependency arrays, or any violations of this checklist. Also use TypeScript’s strict checking to find type errors. Fix all linter errors and ideally adopt a consistent code style (maybe integrate Prettier) so the codebase is uniform.

✅ **“Profile and measure performance.”** – Ask the AI (or team) to use **React DevTools Profiler** to detect components that re-render too often or take too long, then optimize them (using memoization or splitting state) as needed. Also use Next.js’s built-in profiling or logging to see slow serverless functions or DB queries. Utilize the **Next.js Bundle Analyzer** plugin to find any unusually large bundles or dependencies and optimize them (maybe by chunking or removing unused parts). The AI should ensure the final bundle sizes and load times are as low as possible for a smooth UX.

✅ **“Use appropriate tools for state and debugging.”** – In addition to the above, instruct the AI to leverage **Zustand devtools** (if available) to inspect state changes and ensure no unintended re-renders, and to configure **React Hook Form DevTools** (there’s a package) to debug form state if necessary. Ensure that any development-only tools (like React Developer Tools, Redux DevTools) are configured in development and stripped in production. The AI should verify that these tools indicate a healthy, optimized application (e.g., no memory leaks, no extreme CPU usage in profiles, etc.).

By following this checklist and the AI enforcing each point, your project will be up-to-date with 2025’s best practices – resulting in a codebase that is **performant, secure, and maintainable**. Each item above can be verified and fixed without deep technical knowledge from your side: simply hand this list to your AI/development team and have them report on each point. They should provide before/after comparisons, especially for the “Checklist + Examples” option, so you can see the improvements clearly.
