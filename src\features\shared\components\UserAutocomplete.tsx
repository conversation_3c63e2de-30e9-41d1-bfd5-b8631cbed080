'use client';

import { useEffect, useRef, useCallback, useMemo, useReducer } from 'react';
import { Badge } from '@/features/shared/components/ui/badge';
import { X, Loader2 } from 'lucide-react';
import { cn } from '@/lib/utils';
import { searchCache } from '@/lib/cache/search-cache';

interface User {
  id: string;
  email: string;
  name: string;
  role: string;
  status: string;
}

interface UserAutocompleteProps {
  value?: string[] | string;
  onChange?: (value: string[] | string) => void;
  placeholder?: string;
  roleFilter?: string | string[];
  multiple?: boolean;
  disabled?: boolean;
  className?: string;
  error?: string;
  dropdownOnly?: boolean;
  returnUserIds?: boolean;
}

type State = {
  query: string;
  users: User[];
  selectedUsers: User[];
  isLoading: boolean;
  isOpen: boolean;
  hasSearched: boolean;
};

type Action =
  | { type: 'SET_QUERY'; payload: string }
  | { type: 'SET_USERS'; payload: User[] }
  | { type: 'SET_SELECTED_USERS'; payload: User[] }
  | { type: 'SET_IS_LOADING'; payload: boolean }
  | { type: 'SET_IS_OPEN'; payload: boolean }
  | { type: 'SET_HAS_SEARCHED'; payload: boolean }
  | { type: 'RESET_SEARCH' };

const initialState: State = {
  query: '',
  users: [],
  selectedUsers: [],
  isLoading: false,
  isOpen: false,
  hasSearched: false,
};

function reducer(state: State, action: Action): State {
  switch (action.type) {
    case 'SET_QUERY':
      return { ...state, query: action.payload };
    case 'SET_USERS':
      return { ...state, users: action.payload };
    case 'SET_SELECTED_USERS':
      return { ...state, selectedUsers: action.payload };
    case 'SET_IS_LOADING':
      return { ...state, isLoading: action.payload };
    case 'SET_IS_OPEN':
      return { ...state, isOpen: action.payload };
    case 'SET_HAS_SEARCHED':
      return { ...state, hasSearched: action.payload };
    case 'RESET_SEARCH':
      return {
        ...state,
        query: '',
        isOpen: false,
        users: [],
        hasSearched: false,
      };
    default:
      return state;
  }
}

export function UserAutocomplete({
  value,
  onChange,
  placeholder = 'Type email to search users...',
  roleFilter,
  multiple = true,
  disabled = false,
  className,
  error,
  dropdownOnly = false,
  returnUserIds = false,
}: UserAutocompleteProps) {
  const [state, dispatch] = useReducer(reducer, initialState);

  const normalizedValue = useMemo(
    () => (Array.isArray(value) ? value : value ? [value] : []),
    [value]
  );
  const inputRef = useRef<HTMLInputElement>(null);
  const debounceRef = useRef<NodeJS.Timeout | null>(null);

  const searchUsers = useCallback(
    async (searchQuery: string) => {
      const cacheKey = `${searchQuery}:${
        roleFilter ? JSON.stringify(roleFilter) : ''
      }`;
      const cachedUsers = searchCache.get(cacheKey);

      if (cachedUsers) {
        dispatch({ type: 'SET_USERS', payload: cachedUsers });
        dispatch({ type: 'SET_IS_LOADING', payload: false });
        dispatch({ type: 'SET_HAS_SEARCHED', payload: true });
        return;
      }

      dispatch({ type: 'SET_IS_LOADING', payload: true });
      dispatch({ type: 'SET_HAS_SEARCHED', payload: false });

      try {
        const params = new URLSearchParams({ q: searchQuery, limit: '10' });
        if (roleFilter) {
          const allowedRoles = Array.isArray(roleFilter)
            ? roleFilter
            : [roleFilter];
          allowedRoles.forEach((role) => params.append('role', role));
        }

        const response = await fetch(`/api/users/search?${params.toString()}`);
        if (!response.ok) {
          throw new Error('Search failed');
        }

        const data = await response.json();
        const fetchedUsers = data.users || [];
        searchCache.set(cacheKey, fetchedUsers);
        dispatch({ type: 'SET_USERS', payload: fetchedUsers });
      } catch {
        dispatch({ type: 'SET_USERS', payload: [] });
      } finally {
        dispatch({ type: 'SET_IS_LOADING', payload: false });
        dispatch({ type: 'SET_HAS_SEARCHED', payload: true });
      }
    },
    [roleFilter]
  );

  const handleInputChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const newQuery = e.target.value;
      dispatch({ type: 'SET_QUERY', payload: newQuery });

      if (debounceRef.current) {
        clearTimeout(debounceRef.current);
      }

      if (newQuery.length >= 3) {
        dispatch({ type: 'SET_IS_OPEN', payload: true });
        dispatch({ type: 'SET_IS_LOADING', payload: true });
        debounceRef.current = setTimeout(() => {
          searchUsers(newQuery);
        }, 250);
      } else {
        dispatch({ type: 'SET_IS_OPEN', payload: false });
        dispatch({ type: 'SET_USERS', payload: [] });
        dispatch({ type: 'SET_IS_LOADING', payload: false });
        dispatch({ type: 'SET_HAS_SEARCHED', payload: false });
      }
    },
    [searchUsers]
  );

  const { query, selectedUsers } = state;
  const handleUserSelect = useCallback(
    (user: User) => {
      if (multiple) {
        if (!selectedUsers.find((u) => u.id === user.id)) {
          const newSelected = [...selectedUsers, user];
          dispatch({ type: 'SET_SELECTED_USERS', payload: newSelected });
          onChange?.(newSelected.map((u) => (returnUserIds ? u.id : u.email)));
        }
      } else {
        dispatch({ type: 'SET_SELECTED_USERS', payload: [user] });
        onChange?.(returnUserIds ? user.id : user.email);
      }
      dispatch({ type: 'RESET_SEARCH' });
    },
    [multiple, selectedUsers, onChange, returnUserIds]
  );

  const handleUserRemove = useCallback(
    (userId: string) => {
      const newSelected = selectedUsers.filter((u) => u.id !== userId);
      dispatch({ type: 'SET_SELECTED_USERS', payload: newSelected });

      if (multiple) {
        onChange?.(newSelected.map((u) => (returnUserIds ? u.id : u.email)));
      } else {
        onChange?.('');
        dispatch({ type: 'RESET_SEARCH' });
      }
    },
    [selectedUsers, multiple, onChange, returnUserIds]
  );

  useEffect(() => {
    if (normalizedValue.length > 0 && selectedUsers.length === 0) {
      const users = normalizedValue.map((email: string) => ({
        id: email,
        email,
        name: email.split('@')[0] || email,
        role: 'user',
        status: 'active',
      }));
      dispatch({ type: 'SET_SELECTED_USERS', payload: users });
    }
  }, [normalizedValue, selectedUsers.length]);

  const isValidEmail = useCallback((email: string) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }, []);

  const handleKeyDown = useCallback(
    (e: React.KeyboardEvent<HTMLInputElement>) => {
      if (
        (e.key === 'Enter' || e.key === ' ') &&
        query &&
        multiple &&
        !dropdownOnly
      ) {
        e.preventDefault();
        const email = query.trim();
        if (
          isValidEmail(email) &&
          !selectedUsers.find((u) => u.email === email)
        ) {
          const newUser = {
            id: email,
            email,
            name: email.split('@')[0] || email,
            role: 'manual',
            status: 'active',
          };
          const newSelected = [...selectedUsers, newUser];
          dispatch({ type: 'SET_SELECTED_USERS', payload: newSelected });
          onChange?.(newSelected.map((u) => u.email));
          dispatch({ type: 'RESET_SEARCH' });
        }
      }
    },
    [
      query,
      multiple,
      dropdownOnly,
      selectedUsers,
      onChange,
      isValidEmail,
      dispatch,
    ]
  );

  return (
    <div className={cn('relative', className)}>
      <div className='relative'>
        {multiple ? (
          <MultiSelectInput
            state={state}
            dispatch={dispatch}
            inputRef={inputRef}
            handleInputChange={handleInputChange}
            handleKeyDown={handleKeyDown}
            handleUserRemove={handleUserRemove}
            placeholder={placeholder}
            disabled={disabled}
            error={error}
          />
        ) : (
          <SingleSelectInput
            state={state}
            dispatch={dispatch}
            inputRef={inputRef}
            handleInputChange={handleInputChange}
            handleKeyDown={handleKeyDown}
            handleUserRemove={handleUserRemove}
            placeholder={placeholder}
            disabled={disabled}
            error={error}
          />
        )}
      </div>
      <Dropdown state={state} handleUserSelect={handleUserSelect} />
      {error && <p className='mt-1 text-sm text-red-600'>{error}</p>}
    </div>
  );
}

type SubComponentProps = {
  state: State;
  dispatch: React.Dispatch<Action>;
  inputRef: React.RefObject<HTMLInputElement | null>;
  handleInputChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  handleKeyDown: (e: React.KeyboardEvent<HTMLInputElement>) => void;
  handleUserRemove: (userId: string) => void;
  placeholder?: string;
  disabled?: boolean;
  error: string | undefined;
};

const MultiSelectInput = ({
  state,
  dispatch,
  inputRef,
  handleInputChange,
  handleKeyDown,
  handleUserRemove,
  placeholder,
  disabled,
  error,
}: SubComponentProps) => {
  const { query, selectedUsers, isLoading, hasSearched } = state;
  return (
    <div
      className={cn(
        'file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex min-h-10 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm focus-within:border-ring',
        error && 'aria-invalid:border-destructive',
        'flex-wrap gap-1 items-center'
      )}
      onClick={() => inputRef.current?.focus()}
    >
      {selectedUsers.map((user: User) => (
        <Badge
          key={user.id}
          variant='secondary'
          className='flex items-center gap-1 text-xs h-6'
        >
          {user.email}
          <button
            type='button'
            className='ml-1 p-0.5 hover:bg-gray-300 rounded-full transition-colors'
            onClick={(e) => {
              e.stopPropagation();
              handleUserRemove(user.id);
            }}
          >
            <X className='h-3 w-3' />
          </button>
        </Badge>
      ))}
      <input
        ref={inputRef}
        type='text'
        value={query}
        onChange={handleInputChange}
        onKeyDown={handleKeyDown}
        onFocus={() => {
          if (query.length >= 3 && hasSearched) {
            dispatch({ type: 'SET_IS_OPEN', payload: true });
          }
        }}
        onBlur={() =>
          setTimeout(
            () => dispatch({ type: 'SET_IS_OPEN', payload: false }),
            200
          )
        }
        placeholder={selectedUsers.length === 0 ? placeholder : ''}
        disabled={disabled}
        className='flex-1 min-w-0 bg-transparent border-0 outline-none text-base md:text-sm placeholder:text-muted-foreground'
      />
      {isLoading && (
        <Loader2 className='h-4 w-4 animate-spin text-muted-foreground' />
      )}
    </div>
  );
};

const SingleSelectInput = ({
  state,
  dispatch,
  inputRef,
  handleInputChange,
  handleKeyDown,
  handleUserRemove,
  placeholder,
  disabled,
  error,
}: SubComponentProps) => {
  const { query, selectedUsers, isLoading, hasSearched } = state;
  return (
    <div className='relative'>
      {selectedUsers.length > 0 ? (
        <div
          className={cn(
            'file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-10 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm focus-visible:border-ring',
            error && 'aria-invalid:border-destructive',
            'items-center justify-between'
          )}
        >
          <span className='text-gray-900 dark:text-gray-100'>
            {selectedUsers[0]?.email}
          </span>
          <X
            className='h-4 w-4 cursor-pointer hover:bg-gray-300 rounded-full text-gray-500 hover:text-gray-700'
            onClick={(e) => {
              e.stopPropagation();
              handleUserRemove(selectedUsers[0]?.id || '');
            }}
          />
        </div>
      ) : (
        <input
          ref={inputRef}
          type='text'
          value={query}
          onChange={handleInputChange}
          onKeyDown={handleKeyDown}
          onFocus={() => {
            if (query.length >= 3 && hasSearched) {
              dispatch({ type: 'SET_IS_OPEN', payload: true });
            }
          }}
          onBlur={() =>
            setTimeout(
              () => dispatch({ type: 'SET_IS_OPEN', payload: false }),
              200
            )
          }
          placeholder={placeholder}
          disabled={disabled}
          className={cn(
            'file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-10 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm focus-visible:border-ring',
            error && 'aria-invalid:border-destructive'
          )}
        />
      )}
      {isLoading && (
        <Loader2 className='absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 animate-spin' />
      )}
    </div>
  );
};

const Dropdown = ({
  state,
  handleUserSelect,
}: {
  state: State;
  handleUserSelect: (user: User) => void;
}) => {
  const { isOpen, query, isLoading, users, hasSearched } = state;
  if (!isOpen || query.length < 3) return null;

  return (
    <div className='absolute z-10 w-full mt-1 bg-white dark:bg-gray-800 border border-input rounded-md shadow-lg max-h-60 overflow-auto'>
      {isLoading ? (
        <div className='px-3 py-2 text-sm text-gray-500 dark:text-gray-400 flex items-center gap-2'>
          <Loader2 className='h-4 w-4 animate-spin' />
          Searching...
        </div>
      ) : users.length > 0 ? (
        users.map((user: User) => (
          <div
            key={user.id}
            onClick={() => handleUserSelect(user)}
            className='px-3 py-2 cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700 flex items-center justify-between'
          >
            <div>
              <div className='font-medium text-gray-900 dark:text-gray-100'>
                {user.name}
              </div>
              <div className='text-sm text-gray-500 dark:text-gray-400'>
                {user.email}
              </div>
            </div>
            <Badge variant='outline' className='text-xs'>
              {user.role}
            </Badge>
          </div>
        ))
      ) : hasSearched ? (
        <div className='px-3 py-2 text-sm text-gray-500 dark:text-gray-400'>
          No users found matching &ldquo;{query}&rdquo;
        </div>
      ) : null}
    </div>
  );
};
