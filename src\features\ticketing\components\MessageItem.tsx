'use client';

import { ProfileAvatar } from '@/features/shared/components/ProfileAvatar';
import { SafeHtml } from '@/features/shared/components/SafeHtml';
import { Badge } from '@/features/shared/components/ui/badge';
import { cn } from '@/lib/utils';
import { format } from 'date-fns';
import { ChevronDown, ChevronUp } from 'lucide-react';
import { useCallback, useLayoutEffect, useRef, useState } from 'react';
import { Attachment, Ticket } from '../models/ticket.schema';
import { AttachmentItem } from './AttachmentItem';

import AttachmentIndicator from './AttachmentIndicator';

type DisplayMessage = {
  id: string;
  author: {
    name: string;
    email?: string;
    avatarUrl?: string;
  };
  content: string;
  createdAt: Date;
  attachments?: Attachment[];
  isInternal?: boolean;
  isOptimistic?: boolean;
};

interface MessageItemProps {
  message: DisplayMessage;
  isInitiallyExpanded: boolean;
  isLastMessage: boolean;
  currentUserEmail?: string | undefined;
  ticket: Ticket;
}

// Helper function to determine message recipient display
function getMessageRecipientDisplay(
  message: DisplayMessage,
  ticket: Ticket,
  currentUserEmail?: string
): string {
  // Determine who the message is intended for based on ticket context
  let recipientName = '';
  let recipientEmail = '';

  // For admin/super_admin created tickets assigned to agents
  if (
    (ticket.metadata?.createdByUser?.role === 'admin' ||
      ticket.metadata?.createdByUser?.role === 'super_admin') &&
    ticket.assignedTo &&
    ticket.metadata?.assignedUser
  ) {
    // If message is from the ticket creator (admin), it goes to the assigned agent
    if (message.author.email === ticket.metadata.createdByUser.email) {
      recipientName = ticket.metadata.assignedUser.name;
      recipientEmail = ticket.metadata.assignedUser.email;
    }
    // If message is from the assigned agent, it goes back to the admin who created it
    else if (message.author.email === ticket.metadata.assignedUser.email) {
      recipientName = ticket.metadata.createdByUser.name;
      recipientEmail = ticket.metadata.createdByUser.email;
    }
  }
  // For user-created tickets
  else {
    // If message is from the ticket creator (user), it goes to support/admin
    if (message.author.email === ticket.userEmail) {
      recipientName = 'Support Team';
      recipientEmail = 'support';
    }
    // If message is from support/admin, it goes to the user
    else {
      recipientName = ticket.userName;
      recipientEmail = ticket.userEmail;
    }
  }

  // Display logic based on current user
  if (message.author.email === currentUserEmail) {
    // Current user is sending - show "to [recipient]"
    return `to ${recipientName}`;
  } else {
    // Someone else is sending - show "to me" if it's for current user, otherwise show recipient
    if (recipientEmail === currentUserEmail) {
      return 'to me';
    } else {
      return `to ${recipientName}`;
    }
  }
}

export function MessageItem({
  message,
  isInitiallyExpanded,
  isLastMessage,
  currentUserEmail,
  ticket,
}: MessageItemProps) {
  const contentRef = useRef<HTMLDivElement>(null);
  const [isExpanded, setIsExpanded] = useState(isInitiallyExpanded);
  const [isCollapsible, setIsCollapsible] = useState(false);

  const canBeCollapsible = !isLastMessage;

  const analyzeContent = useCallback((html: string) => {
    if (typeof window === 'undefined' || !html) {
      return { isMultiBlock: false, text: '', sanitizedHtml: '' };
    }
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = html;
    const text = tempDiv.textContent || '';
    const blockElements = tempDiv.querySelectorAll(
      'p, h1, h2, h3, h4, h5, h6, ul, ol, li, blockquote, br'
    );
    const isMultiBlock = blockElements.length > 1;

    // Always preserve the original HTML formatting
    // Don't convert to plain text even for single blocks
    return { isMultiBlock, text, sanitizedHtml: html };
  }, []);

  useLayoutEffect(() => {
    if (!canBeCollapsible) {
      setIsCollapsible(false);
      setIsExpanded(true);
      return;
    }

    const element = contentRef.current;
    if (element) {
      const { isMultiBlock, text } = analyzeContent(message.content);

      if (isMultiBlock) {
        setIsCollapsible(true);
        setIsExpanded(isInitiallyExpanded);
        return;
      }

      const tempElement = document.createElement('p');
      tempElement.style.position = 'absolute';
      tempElement.style.visibility = 'hidden';
      tempElement.style.whiteSpace = 'nowrap';
      tempElement.style.fontSize = '0.875rem';
      tempElement.style.fontFamily = getComputedStyle(element).fontFamily;
      tempElement.textContent = text;

      document.body.appendChild(tempElement);
      const textWidth = tempElement.offsetWidth;
      document.body.removeChild(tempElement);

      const containerWidth = element.offsetWidth;
      const isOverflowing = textWidth > containerWidth;

      setIsCollapsible(isOverflowing);
      setIsExpanded(isOverflowing ? isInitiallyExpanded : true);
    }
  }, [canBeCollapsible, isInitiallyExpanded, analyzeContent, message.content]);

  const toggleExpansion = () => {
    if (isCollapsible) {
      setIsExpanded((prev) => !prev);
    }
  };

  const { text: plainText, sanitizedHtml } = analyzeContent(message.content);

  return (
    <div
      className={cn(
        'bg-white dark:bg-gray-800 transition-opacity duration-300',
        isCollapsible &&
          !isExpanded &&
          'cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700/50',
        message.isOptimistic && 'opacity-70'
      )}
      onClick={isCollapsible && !isExpanded ? toggleExpansion : undefined}
    >
      <div className='flex gap-3 py-4 px-6 transition-colors'>
        <ProfileAvatar
          avatarUrl={message.author.avatarUrl ?? null}
          name={message.author.name}
          email={message.author.email ?? ''}
          className='h-10 w-10 mt-1'
        />
        <div className='flex-1 min-w-0'>
          <div className='flex items-start justify-between mb-1'>
            <div className='flex-1'>
              <div className='flex items-center gap-2'>
                <h4 className='font-medium text-gray-900 dark:text-gray-100 text-sm'>
                  {message.author.name}
                </h4>
                <span className='text-xs text-gray-500 dark:text-gray-400'>
                  {format(new Date(message.createdAt), "do 'of' MMMM 'at' h a")}
                </span>
                {message.isInternal && (
                  <Badge variant='secondary' className='text-xs'>
                    Internal Note
                  </Badge>
                )}
              </div>
              {(isLastMessage || (isCollapsible && isExpanded)) &&
                message.author.email && (
                  <p className='text-sm text-gray-500 dark:text-gray-400 mt-0.5'>
                    {getMessageRecipientDisplay(
                      message,
                      ticket,
                      currentUserEmail
                    )}
                  </p>
                )}
            </div>
            <div className='flex items-center gap-4 ml-auto shrink-0'>
              {/* Attachment indicator - only visible when collapsed and attachments exist */}
              {isCollapsible && !isExpanded && (
                <AttachmentIndicator count={message.attachments?.length || 0} />
              )}

              {isCollapsible && (
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    toggleExpansion();
                  }}
                  className='flex items-center gap-1 text-xs text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 cursor-pointer'
                >
                  <span>{isExpanded ? 'Collapse' : 'Expand'}</span>
                  {isExpanded ? (
                    <ChevronUp className='h-3 w-3' />
                  ) : (
                    <ChevronDown className='h-3 w-3' />
                  )}
                </button>
              )}
            </div>
          </div>
          <div
            ref={contentRef}
            className={cn(
              'text-sm text-gray-600 dark:text-gray-400',
              isExpanded ? 'mt-2' : ''
            )}
          >
            {isExpanded ? (
              <SafeHtml
                content={sanitizedHtml}
                className='[&_p]:whitespace-pre-wrap'
              />
            ) : (
              <p className='truncate'>{plainText}</p>
            )}
          </div>

          {isExpanded &&
            message.attachments &&
            message.attachments.length > 0 && (
              <div className='mt-4'>
                <h4 className='text-sm font-medium text-gray-900 dark:text-gray-100 mt-8 mb-3'>
                  {message.attachments.length} Attachments
                </h4>
                <div className='flex flex-wrap gap-3'>
                  {message.attachments.map((attachment) => (
                    <AttachmentItem
                      key={attachment.id}
                      attachment={attachment}
                    />
                  ))}
                </div>
              </div>
            )}
        </div>
      </div>
    </div>
  );
}
